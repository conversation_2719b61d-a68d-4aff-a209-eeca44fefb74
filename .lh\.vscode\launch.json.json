{"sourceFile": ".vscode/launch.json", "activeCommit": 0, "commits": [{"activePatchIndex": 8, "patches": [{"date": 1750233409200, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750233648981, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,19 +1,30 @@\n {\r\n   \"version\": \"0.2.0\",\r\n   \"configurations\": [\r\n     {\r\n-      \"name\": \"Debug Main Process\",\r\n+      \"name\": \"Electron: Main\",\r\n       \"type\": \"node\",\r\n       \"request\": \"launch\",\r\n-      \"cwd\": \"${workspaceRoot}\",\r\n-      \"runtimeExecutable\": \"${workspaceRoot}/node_modules/.bin/electron-vite\",\r\n+      \"program\": \"${workspaceFolder}/node_modules/.pnpm/electron@30.5.1/node_modules/electron/cli.js\",\r\n+      \"args\": [\".\"],\r\n+      \"cwd\": \"${workspaceFolder}\",\r\n+      \"sourceMaps\": true,\r\n+      \"protocol\": \"inspector\",\r\n+      \"runtimeExecutable\": null,\r\n       \"windows\": {\r\n-        \"runtimeExecutable\": \"${workspaceRoot}/node_modules/.bin/electron-vite.cmd\"\r\n+        \"runtimeExecutable\": \"${workspaceFolder}/node_modules/.pnpm/electron@30.5.1/node_modules/.bin/electron.cmd\"\r\n       },\r\n-      \"runtimeArgs\": [\"--sourcemap\"],\r\n       \"env\": {\r\n-        \"REMOTE_DEBUGGING_PORT\": \"9222\"\r\n-      }\r\n+        \"VITE_DEV_SERVER_URL\": \"http://localhost:5173/\"\r\n+      },\r\n+      \"outputCapture\": \"std\"\r\n+    },\r\n+    {\r\n+      \"name\": \"Electron: Renderer\",\r\n+      \"type\": \"chrome\",\r\n+      \"request\": \"launch\",\r\n+      \"url\": \"http://localhost:5173\",\r\n+      \"webRoot\": \"${workspaceFolder}\"\r\n     }\r\n   ]\r\n }\r\n"}, {"date": 1750233687547, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,12 +9,9 @@\n       \"args\": [\".\"],\r\n       \"cwd\": \"${workspaceFolder}\",\r\n       \"sourceMaps\": true,\r\n       \"protocol\": \"inspector\",\r\n-      \"runtimeExecutable\": null,\r\n-      \"windows\": {\r\n-        \"runtimeExecutable\": \"${workspaceFolder}/node_modules/.pnpm/electron@30.5.1/node_modules/.bin/electron.cmd\"\r\n-      },\r\n+      \"runtimeExecutable\": \"${workspaceFolder}/node_modules/.pnpm/electron@30.5.1/node_modules/.bin/electron.cmd\",\r\n       \"env\": {\r\n         \"VITE_DEV_SERVER_URL\": \"http://localhost:5173/\"\r\n       },\r\n       \"outputCapture\": \"std\"\r\n"}, {"date": 1750233694785, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,9 @@\n {\r\n   \"version\": \"0.2.0\",\r\n   \"configurations\": [\r\n     {\r\n-      \"name\": \"Electron: Main\",\r\n+      \"name\": \"Main\",\r\n       \"type\": \"node\",\r\n       \"request\": \"launch\",\r\n       \"program\": \"${workspaceFolder}/node_modules/.pnpm/electron@30.5.1/node_modules/electron/cli.js\",\r\n       \"args\": [\".\"],\r\n@@ -16,9 +16,9 @@\n       },\r\n       \"outputCapture\": \"std\"\r\n     },\r\n     {\r\n-      \"name\": \"Electron: Renderer\",\r\n+      \"name\": \"Renderer\",\r\n       \"type\": \"chrome\",\r\n       \"request\": \"launch\",\r\n       \"url\": \"http://localhost:5173\",\r\n       \"webRoot\": \"${workspaceFolder}\"\r\n"}, {"date": 1750233793108, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,9 +8,8 @@\n       \"program\": \"${workspaceFolder}/node_modules/.pnpm/electron@30.5.1/node_modules/electron/cli.js\",\r\n       \"args\": [\".\"],\r\n       \"cwd\": \"${workspaceFolder}\",\r\n       \"sourceMaps\": true,\r\n-      \"protocol\": \"inspector\",\r\n       \"runtimeExecutable\": \"${workspaceFolder}/node_modules/.pnpm/electron@30.5.1/node_modules/.bin/electron.cmd\",\r\n       \"env\": {\r\n         \"VITE_DEV_SERVER_URL\": \"http://localhost:5173/\"\r\n       },\r\n"}, {"date": 1750233871705, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,26 +1,68 @@\n {\r\n   \"version\": \"0.2.0\",\r\n+  \"compounds\": [\r\n+    {\r\n+      \"name\": \"Debug App\",\r\n+      \"preLaunchTask\": \"Before Debug\",\r\n+      \"configurations\": [\r\n+        \"Debug Main Process\",\r\n+        \"Debug Renderer Process\"\r\n+      ],\r\n+      \"presentation\": {\r\n+        \"hidden\": false,\r\n+        \"group\": \"\",\r\n+        \"order\": 1\r\n+      },\r\n+      \"stopAll\": true\r\n+    }\r\n+  ],\r\n   \"configurations\": [\r\n     {\r\n-      \"name\": \"Main\",\r\n+      \"name\": \"Debug Main Process\",\r\n       \"type\": \"node\",\r\n       \"request\": \"launch\",\r\n-      \"program\": \"${workspaceFolder}/node_modules/.pnpm/electron@30.5.1/node_modules/electron/cli.js\",\r\n-      \"args\": [\".\"],\r\n-      \"cwd\": \"${workspaceFolder}\",\r\n-      \"sourceMaps\": true,\r\n-      \"runtimeExecutable\": \"${workspaceFolder}/node_modules/.pnpm/electron@30.5.1/node_modules/.bin/electron.cmd\",\r\n-      \"env\": {\r\n-        \"VITE_DEV_SERVER_URL\": \"http://localhost:5173/\"\r\n+      \"runtimeExecutable\": \"${workspaceRoot}/node_modules/.bin/electron\",\r\n+      \"windows\": {\r\n+        \"runtimeExecutable\": \"${workspaceRoot}/node_modules/.bin/electron.cmd\"\r\n       },\r\n-      \"outputCapture\": \"std\"\r\n+      \"runtimeArgs\": [\r\n+        \"--remote-debugging-port=9229\",\r\n+        \".\"\r\n+      ],\r\n+      \"envFile\": \"${workspaceFolder}/.vscode/.debug.env\",\r\n+      \"console\": \"integratedTerminal\"\r\n     },\r\n     {\r\n-      \"name\": \"Renderer\",\r\n+      \"name\": \"Debug Renderer Process\",\r\n+      \"port\": 9229,\r\n+      \"request\": \"attach\",\r\n       \"type\": \"chrome\",\r\n+      \"timeout\": 60000,\r\n+      \"skipFiles\": [\r\n+        \"<node_internals>/**\",\r\n+        \"${workspaceRoot}/node_modules/**\",\r\n+        \"${workspaceRoot}/dist-electron/**\",\r\n+        \"http://127.0.0.1:3344/**\"\r\n+      ]\r\n+    },\r\n+    {\r\n+      \"name\": \"C/C++ Runner: Debug Session\",\r\n+      \"type\": \"cppdbg\",\r\n       \"request\": \"launch\",\r\n-      \"url\": \"http://localhost:5173\",\r\n-      \"webRoot\": \"${workspaceFolder}\"\r\n+      \"args\": [],\r\n+      \"stopAtEntry\": false,\r\n+      \"externalConsole\": true,\r\n+      \"cwd\": \"d:/GITHUB-DAWN-LAUNCHER/DawnLauncher\",\r\n+      \"program\": \"d:/GITHUB-DAWN-LAUNCHER/DawnLauncher/build/Debug/outDebug\",\r\n+      \"MIMode\": \"gdb\",\r\n+      \"miDebuggerPath\": \"gdb\",\r\n+      \"setupCommands\": [\r\n+        {\r\n+          \"description\": \"Enable pretty-printing for gdb\",\r\n+          \"text\": \"-enable-pretty-printing\",\r\n+          \"ignoreFailures\": true\r\n+        }\r\n+      ]\r\n     }\r\n   ]\r\n-}\r\n+}\n\\ No newline at end of file\n"}, {"date": 1750233919314, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,25 +44,7 @@\n         \"${workspaceRoot}/dist-electron/**\",\r\n         \"http://127.0.0.1:3344/**\"\r\n       ]\r\n     },\r\n-    {\r\n-      \"name\": \"C/C++ Runner: Debug Session\",\r\n-      \"type\": \"cppdbg\",\r\n-      \"request\": \"launch\",\r\n-      \"args\": [],\r\n-      \"stopAtEntry\": false,\r\n-      \"externalConsole\": true,\r\n-      \"cwd\": \"d:/GITHUB-DAWN-LAUNCHER/DawnLauncher\",\r\n-      \"program\": \"d:/GITHUB-DAWN-LAUNCHER/DawnLauncher/build/Debug/outDebug\",\r\n-      \"MIMode\": \"gdb\",\r\n-      \"miDebuggerPath\": \"gdb\",\r\n-      \"setupCommands\": [\r\n-        {\r\n-          \"description\": \"Enable pretty-printing for gdb\",\r\n-          \"text\": \"-enable-pretty-printing\",\r\n-          \"ignoreFailures\": true\r\n-        }\r\n-      ]\r\n-    }\r\n+\r\n   ]\r\n }\n\\ No newline at end of file\n"}, {"date": 1750234001765, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,22 +1,6 @@\n {\r\n   \"version\": \"0.2.0\",\r\n-  \"compounds\": [\r\n-    {\r\n-      \"name\": \"Debug App\",\r\n-      \"preLaunchTask\": \"Before Debug\",\r\n-      \"configurations\": [\r\n-        \"Debug Main Process\",\r\n-        \"Debug Renderer Process\"\r\n-      ],\r\n-      \"presentation\": {\r\n-        \"hidden\": false,\r\n-        \"group\": \"\",\r\n-        \"order\": 1\r\n-      },\r\n-      \"stopAll\": true\r\n-    }\r\n-  ],\r\n   \"configurations\": [\r\n     {\r\n       \"name\": \"Debug Main Process\",\r\n       \"type\": \"node\",\r\n@@ -43,8 +27,7 @@\n         \"${workspaceRoot}/node_modules/**\",\r\n         \"${workspaceRoot}/dist-electron/**\",\r\n         \"http://127.0.0.1:3344/**\"\r\n       ]\r\n-    },\r\n-\r\n+    }\r\n   ]\r\n }\n\\ No newline at end of file\n"}, {"date": 1750234367743, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,33 @@\n+// {\r\n+//   \"version\": \"0.2.0\",\r\n+//   \"configurations\": [\r\n+//     {\r\n+//       \"name\": \"Debug Main Process\",\r\n+//       \"type\": \"node\",\r\n+//       \"request\": \"launch\",\r\n+//       \"runtimeExecutable\": \"${workspaceRoot}/node_modules/.bin/electron\",\r\n+//       \"windows\": {\r\n+//         \"runtimeExecutable\": \"${workspaceRoot}/node_modules/.bin/electron.cmd\"\r\n+//       },\r\n+//       \"runtimeArgs\": [\r\n+//         \"--remote-debugging-port=9229\",\r\n+//         \".\"\r\n+//       ],\r\n+//       \"envFile\": \"${workspaceFolder}/.vscode/.debug.env\",\r\n+//       \"console\": \"integratedTerminal\"\r\n+//     },\r\n+//     {\r\n+//       \"name\": \"Debug Renderer Process\",\r\n+//       \"port\": 9229,\r\n+//       \"request\": \"attach\",\r\n+//       \"type\": \"chrome\",\r\n+//       \"timeout\": 60000,\r\n+//       \"skipFiles\": [\r\n+//         \"<node_internals>/**\",\r\n+//         \"${workspaceRoot}/node_modules/**\",\r\n+//         \"${workspaceRoot}/dist-electron/**\",\r\n+//         \"http://127.0.0.1:3344/**\"\r\n+//       ]\r\n+//     }\r\n+//   ]\r\n+// }\n\\ No newline at end of file\n"}], "date": 1750233409200, "name": "Commit-0", "content": "{\r\n  \"version\": \"0.2.0\",\r\n  \"configurations\": [\r\n    {\r\n      \"name\": \"Debug Main Process\",\r\n      \"type\": \"node\",\r\n      \"request\": \"launch\",\r\n      \"cwd\": \"${workspaceRoot}\",\r\n      \"runtimeExecutable\": \"${workspaceRoot}/node_modules/.bin/electron-vite\",\r\n      \"windows\": {\r\n        \"runtimeExecutable\": \"${workspaceRoot}/node_modules/.bin/electron-vite.cmd\"\r\n      },\r\n      \"runtimeArgs\": [\"--sourcemap\"],\r\n      \"env\": {\r\n        \"REMOTE_DEBUGGING_PORT\": \"9222\"\r\n      }\r\n    }\r\n  ]\r\n}\r\n"}]}