{"sourceFile": "plugins/example/index.js", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750233214773, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750233214773, "name": "Commit-0", "content": "export function activate() {\r\n  console.log('Plugin Activated!');\r\n  return {\r\n    feature: () => {\r\n      console.log('This is a feature provided by the plugin.');\r\n    },\r\n  };\r\n}\r\n\r\nexport function deactivate() {\r\n  console.log('Plugin Deactivated!');\r\n}\r\n"}]}