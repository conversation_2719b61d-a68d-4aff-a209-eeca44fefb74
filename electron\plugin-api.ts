/**
 * 插件API模块
 * 为插件提供丰富的系统接口
 */

import { app, dialog, shell, BrowserWindow, Notification } from 'electron';
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * 插件API接口
 */
export interface PluginAPI {
  // 系统信息
  system: {
    getVersion(): string;
    getPlatform(): string;
    getArch(): string;
    getAppVersion(): string;
  };
  
  // 文件系统操作
  fs: {
    readFile(filePath: string): Promise<string>;
    writeFile(filePath: string, content: string): Promise<void>;
    exists(filePath: string): boolean;
    mkdir(dirPath: string): Promise<void>;
    readdir(dirPath: string): Promise<string[]>;
    stat(filePath: string): Promise<fs.Stats>;
  };
  
  // 对话框
  dialog: {
    showMessageBox(options: {
      type?: 'info' | 'warning' | 'error' | 'question';
      title?: string;
      message: string;
      detail?: string;
      buttons?: string[];
    }): Promise<{ response: number; checkboxChecked: boolean }>;
    showOpenDialog(options: {
      title?: string;
      defaultPath?: string;
      filters?: Array<{ name: string; extensions: string[] }>;
      properties?: Array<'openFile' | 'openDirectory' | 'multiSelections'>;
    }): Promise<{ canceled: boolean; filePaths: string[] }>;
    showSaveDialog(options: {
      title?: string;
      defaultPath?: string;
      filters?: Array<{ name: string; extensions: string[] }>;
    }): Promise<{ canceled: boolean; filePath?: string }>;
  };
  
  // 通知
  notification: {
    show(options: {
      title: string;
      body: string;
      icon?: string;
      silent?: boolean;
    }): void;
  };
  
  // 外部应用
  shell: {
    openExternal(url: string): Promise<void>;
    openPath(path: string): Promise<string>;
    showItemInFolder(fullPath: string): void;
  };
  
  // 命令执行
  exec: {
    run(command: string, options?: { cwd?: string; timeout?: number }): Promise<{
      stdout: string;
      stderr: string;
    }>;
  };
  
  // 窗口操作
  window: {
    getCurrentWindow(): BrowserWindow | null;
    createWindow(options: {
      width?: number;
      height?: number;
      title?: string;
      url?: string;
    }): BrowserWindow;
    closeWindow(windowId?: number): void;
  };
  
  // 日志记录
  logger: {
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    debug(message: string, ...args: any[]): void;
  };
  
  // 存储
  storage: {
    get(key: string): any;
    set(key: string, value: any): void;
    remove(key: string): void;
    clear(): void;
  };
  
  // 事件系统
  events: {
    emit(eventName: string, ...args: any[]): void;
    on(eventName: string, listener: (...args: any[]) => void): void;
    off(eventName: string, listener?: (...args: any[]) => void): void;
    once(eventName: string, listener: (...args: any[]) => void): void;
  };
}

/**
 * 插件存储管理
 */
class PluginStorage {
  private storage: Map<string, any> = new Map();
  private pluginName: string;
  
  constructor(pluginName: string) {
    this.pluginName = pluginName;
    this.loadFromFile();
  }
  
  private getStorageFile(): string {
    const appDataPath = app.getPath('userData');
    const pluginDataDir = path.join(appDataPath, 'plugin-data');
    if (!fs.existsSync(pluginDataDir)) {
      fs.mkdirSync(pluginDataDir, { recursive: true });
    }
    return path.join(pluginDataDir, `${this.pluginName}.json`);
  }
  
  private loadFromFile(): void {
    try {
      const storageFile = this.getStorageFile();
      if (fs.existsSync(storageFile)) {
        const data = JSON.parse(fs.readFileSync(storageFile, 'utf-8'));
        this.storage = new Map(Object.entries(data));
      }
    } catch (error) {
      console.warn(`Failed to load storage for plugin ${this.pluginName}:`, error);
    }
  }
  
  private saveToFile(): void {
    try {
      const storageFile = this.getStorageFile();
      const data = Object.fromEntries(this.storage);
      fs.writeFileSync(storageFile, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Failed to save storage for plugin ${this.pluginName}:`, error);
    }
  }
  
  get(key: string): any {
    return this.storage.get(key);
  }
  
  set(key: string, value: any): void {
    this.storage.set(key, value);
    this.saveToFile();
  }
  
  remove(key: string): void {
    this.storage.delete(key);
    this.saveToFile();
  }
  
  clear(): void {
    this.storage.clear();
    this.saveToFile();
  }
}

/**
 * 插件事件管理器
 */
class PluginEventManager {
  private listeners: Map<string, Set<(...args: any[]) => void>> = new Map();
  
  emit(eventName: string, ...args: any[]): void {
    const eventListeners = this.listeners.get(eventName);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(...args);
        } catch (error) {
          console.error(`Error in event listener for ${eventName}:`, error);
        }
      });
    }
  }
  
  on(eventName: string, listener: (...args: any[]) => void): void {
    if (!this.listeners.has(eventName)) {
      this.listeners.set(eventName, new Set());
    }
    this.listeners.get(eventName)!.add(listener);
  }
  
  off(eventName: string, listener?: (...args: any[]) => void): void {
    if (!listener) {
      this.listeners.delete(eventName);
    } else {
      const eventListeners = this.listeners.get(eventName);
      if (eventListeners) {
        eventListeners.delete(listener);
        if (eventListeners.size === 0) {
          this.listeners.delete(eventName);
        }
      }
    }
  }
  
  once(eventName: string, listener: (...args: any[]) => void): void {
    const onceListener = (...args: any[]) => {
      listener(...args);
      this.off(eventName, onceListener);
    };
    this.on(eventName, onceListener);
  }
}

// 全局事件管理器
const globalEventManager = new PluginEventManager();

/**
 * 创建插件API实例
 */
export function createPluginAPI(pluginName: string): PluginAPI {
  const storage = new PluginStorage(pluginName);
  
  return {
    system: {
      getVersion: () => process.version,
      getPlatform: () => process.platform,
      getArch: () => process.arch,
      getAppVersion: () => app.getVersion(),
    },
    
    fs: {
      readFile: (filePath: string) => fs.promises.readFile(filePath, 'utf-8'),
      writeFile: (filePath: string, content: string) => fs.promises.writeFile(filePath, content, 'utf-8'),
      exists: (filePath: string) => fs.existsSync(filePath),
      mkdir: (dirPath: string) => fs.promises.mkdir(dirPath, { recursive: true }),
      readdir: (dirPath: string) => fs.promises.readdir(dirPath),
      stat: (filePath: string) => fs.promises.stat(filePath),
    },
    
    dialog: {
      showMessageBox: async (options) => {
        const result = await dialog.showMessageBox(options);
        return result;
      },
      showOpenDialog: async (options) => {
        const result = await dialog.showOpenDialog(options);
        return result;
      },
      showSaveDialog: async (options) => {
        const result = await dialog.showSaveDialog(options);
        return result;
      },
    },
    
    notification: {
      show: (options) => {
        const notification = new Notification(options);
        notification.show();
      },
    },
    
    shell: {
      openExternal: (url: string) => shell.openExternal(url),
      openPath: (path: string) => shell.openPath(path),
      showItemInFolder: (fullPath: string) => shell.showItemInFolder(fullPath),
    },
    
    exec: {
      run: async (command: string, options = {}) => {
        const result = await execAsync(command, options);
        return result;
      },
    },
    
    window: {
      getCurrentWindow: () => BrowserWindow.getFocusedWindow(),
      createWindow: (options) => {
        const win = new BrowserWindow({
          width: options.width || 800,
          height: options.height || 600,
          title: options.title || 'Plugin Window',
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
          },
        });
        
        if (options.url) {
          win.loadURL(options.url);
        }
        
        return win;
      },
      closeWindow: (windowId?: number) => {
        const win = windowId 
          ? BrowserWindow.fromId(windowId)
          : BrowserWindow.getFocusedWindow();
        if (win) {
          win.close();
        }
      },
    },
    
    logger: {
      info: (message: string, ...args: any[]) => console.log(`[${pluginName}] INFO:`, message, ...args),
      warn: (message: string, ...args: any[]) => console.warn(`[${pluginName}] WARN:`, message, ...args),
      error: (message: string, ...args: any[]) => console.error(`[${pluginName}] ERROR:`, message, ...args),
      debug: (message: string, ...args: any[]) => console.debug(`[${pluginName}] DEBUG:`, message, ...args),
    },
    
    storage: {
      get: (key: string) => storage.get(key),
      set: (key: string, value: any) => storage.set(key, value),
      remove: (key: string) => storage.remove(key),
      clear: () => storage.clear(),
    },
    
    events: {
      emit: (eventName: string, ...args: any[]) => globalEventManager.emit(eventName, ...args),
      on: (eventName: string, listener: (...args: any[]) => void) => globalEventManager.on(eventName, listener),
      off: (eventName: string, listener?: (...args: any[]) => void) => globalEventManager.off(eventName, listener),
      once: (eventName: string, listener: (...args: any[]) => void) => globalEventManager.once(eventName, listener),
    },
  };
}
