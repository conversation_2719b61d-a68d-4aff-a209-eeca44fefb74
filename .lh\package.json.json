{"sourceFile": "package.json", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750232764584, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750232764584, "name": "Commit-0", "content": "{\n  \"name\": \"terminal\",\n  \"private\": true,\n  \"version\": \"0.0.0\",\n  \"type\": \"module\",\n  \"scripts\": {\n    \"dev\": \"vite\",\n    \"build\": \"vue-tsc && vite build && electron-builder\",\n    \"preview\": \"vite preview\"\n  },\n  \"dependencies\": {\n    \"vue\": \"^3.4.21\"\n  },\n  \"devDependencies\": {\n    \"@vitejs/plugin-vue\": \"^5.0.4\",\n    \"typescript\": \"^5.2.2\",\n    \"vite\": \"^5.1.6\",\n    \"vue-tsc\": \"^2.0.26\",\n    \"electron\": \"^30.0.1\",\n    \"electron-builder\": \"^24.13.3\",\n    \"vite-plugin-electron\": \"^0.28.6\",\n    \"vite-plugin-electron-renderer\": \"^0.14.5\"\n  },\n  \"main\": \"dist-electron/main.js\",\n  \"pnpm\": {\n    \"onlyBuiltDependencies\": [\n      \"@parcel/watcher\",\n      \"cpu-features\",\n      \"electron\",\n      \"esbuild\",\n      \"node-pty\",\n      \"simple-git-hooks\",\n      \"ssh2\"\n    ]\n  }\n}\n"}]}