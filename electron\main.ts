import { app, BrowserWindow, ipcMain } from 'electron'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import fs from 'fs'
import type {
  PluginManifest,
  PluginInfo,
  PluginFeatures,
  PluginLoadStatus,
  PluginOperationResult,
  PluginModule
} from '../src/types/plugin'
import { createPluginAPI } from './plugin-api'
import { securityManager } from './plugin-security'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.mjs
// │
process.env.APP_ROOT = path.join(__dirname, '..')

// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST

let win: BrowserWindow | null

function createWindow() {
  win = new BrowserWindow({
    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.mjs'),
    },
  })

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())
  })

  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL)
  } else {
    // win.loadFile('dist/index.html')
    win.loadFile(path.join(RENDERER_DIST, 'index.html'))
  }
}

// 插件功能存储：插件名 -> 功能集合
const pluginFeatures: Record<string, PluginFeatures> = {};

// 插件加载状态跟踪
const pluginLoadStatus: Map<string, PluginLoadStatus> = new Map();

// 文件监听器存储
const pluginWatchers: Map<string, fs.FSWatcher> = new Map();

// 热重载开关
let hotReloadEnabled = process.env.NODE_ENV === 'development';

/**
 * 重新加载单个插件
 */
async function reloadPlugin(pluginName: string, pluginPath: string) {
  try {
    console.log(`Hot reloading plugin: ${pluginName}`);

    // 停用现有插件
    if (pluginFeatures[pluginName]) {
      try {
        const manifestPath = path.join(pluginPath, 'manifest.json');
        if (fs.existsSync(manifestPath)) {
          const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8')) as PluginManifest;
          const pluginEntry = path.join(pluginPath, manifest.main);

          // 清除模块缓存
          delete require.cache[require.resolve(pluginEntry)];

          // 尝试调用deactivate
          const pluginEntryUrl = `file://${pluginEntry.replace(/\\/g, '/')}`;
          const oldPlugin = await import(pluginEntryUrl) as PluginModule;
          if (typeof oldPlugin.deactivate === 'function') {
            await oldPlugin.deactivate();
          }
        }
      } catch (deactivateError) {
        console.warn(`Warning: Failed to deactivate plugin "${pluginName}":`, deactivateError);
      }

      // 从内存中移除
      delete pluginFeatures[pluginName];
    }

    // 重新加载插件
    await loadSinglePlugin(pluginName, pluginPath);

    // 通知渲染进程插件已重新加载
    if (win && !win.isDestroyed()) {
      win.webContents.send('plugin-reloaded', { pluginName, status: 'success' });
    }

  } catch (error) {
    console.error(`Failed to reload plugin "${pluginName}":`, error);
    pluginLoadStatus.set(pluginName, {
      name: pluginName,
      status: 'failed',
      error: error instanceof Error ? error.message : 'Hot reload failed'
    });

    // 通知渲染进程重载失败
    if (win && !win.isDestroyed()) {
      win.webContents.send('plugin-reloaded', {
        pluginName,
        status: 'error',
        error: error instanceof Error ? error.message : 'Hot reload failed'
      });
    }
  }
}

/**
 * 加载单个插件
 */
async function loadSinglePlugin(_pluginName: string, pluginPath: string) {
  const manifestPath = path.join(pluginPath, 'manifest.json');

  if (!fs.existsSync(manifestPath)) {
    throw new Error(`Manifest file not found: ${manifestPath}`);
  }

  const manifestContent = fs.readFileSync(manifestPath, 'utf-8');
  let manifest: PluginManifest;

  try {
    manifest = JSON.parse(manifestContent) as PluginManifest;
  } catch (parseError) {
    const errorMessage = parseError instanceof Error ? parseError.message : 'Invalid JSON format';
    throw new Error(`Invalid JSON in manifest: ${errorMessage}`);
  }

  // 验证清单文件
  const manifestValidation = securityManager.validateManifest(manifest);
  if (!manifestValidation.valid) {
    throw new Error(`Invalid manifest: ${manifestValidation.errors.join(', ')}`);
  }

  pluginLoadStatus.set(manifest.name, { name: manifest.name, status: 'loading' });

  const pluginEntry = path.join(pluginPath, manifest.main);
  if (!fs.existsSync(pluginEntry)) {
    throw new Error(`Plugin entry file not found: ${pluginEntry}`);
  }

  // 验证插件文件安全性
  const fileValidation = await securityManager.validatePluginFile(pluginEntry);
  if (!fileValidation.valid) {
    throw new Error(`Plugin file validation failed: ${fileValidation.errors.join(', ')}`);
  }

  // 使用文件URL来导入ES模块
  const pluginEntryUrl = `file://${pluginEntry.replace(/\\/g, '/')}`;
  const plugin = await import(pluginEntryUrl) as PluginModule;

  if (typeof plugin.activate !== 'function') {
    throw new Error('Plugin must export an "activate" function');
  }

  // 创建插件API实例
  const rawPluginAPI = createPluginAPI(manifest.name);

  // 创建安全的API代理
  const securePluginAPI = securityManager.createSecureAPIProxy(manifest.name, rawPluginAPI, manifest);

  // 调用插件的activate函数，传入安全API
  const features = await plugin.activate(securePluginAPI);
  if (features && typeof features === 'object') {
    pluginFeatures[manifest.name] = features;
    pluginLoadStatus.set(manifest.name, {
      name: manifest.name,
      status: 'loaded',
      loadedAt: Date.now()
    });

    // 设置热重载监听器
    if (hotReloadEnabled) {
      setupHotReload(manifest.name, pluginPath);
    }

    console.log(`Plugin "${manifest.name}" loaded successfully`);
  } else {
    throw new Error('Plugin activate function must return an object with features');
  }
}

/**
 * 设置插件热重载监听
 */
function setupHotReload(pluginName: string, pluginPath: string) {
  // 清除现有监听器
  if (pluginWatchers.has(pluginName)) {
    pluginWatchers.get(pluginName)?.close();
  }

  try {
    const watcher = fs.watch(pluginPath, { recursive: true }, (_, filename) => {
      if (filename && (filename.endsWith('.js') || filename.endsWith('.json'))) {
        console.log(`Plugin file changed: ${filename} in ${pluginName}`);

        // 防抖：延迟重载以避免频繁触发
        setTimeout(() => {
          reloadPlugin(pluginName, pluginPath);
        }, 500);
      }
    });

    pluginWatchers.set(pluginName, watcher);
    console.log(`Hot reload enabled for plugin: ${pluginName}`);
  } catch (error) {
    console.warn(`Failed to setup hot reload for plugin "${pluginName}":`, error);
  }
}

async function loadPlugins() {
  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');
  if (!fs.existsSync(pluginsDir)) {
    console.log('Plugins directory not found, creating...');
    fs.mkdirSync(pluginsDir, { recursive: true });
    return;
  }

  try {
    const pluginFolders = fs.readdirSync(pluginsDir);
    const loadPromises = pluginFolders.map(async (folder) => {
      const pluginPath = path.join(pluginsDir, folder);

      // 跳过非目录文件
      if (!fs.statSync(pluginPath).isDirectory()) {
        return;
      }

      try {
        await loadSinglePlugin(folder, pluginPath);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error(`Failed to load plugin "${folder}":`, errorMessage);
        pluginLoadStatus.set(folder, {
          name: folder,
          status: 'failed',
          error: errorMessage
        });
      }
    });

    await Promise.allSettled(loadPromises);
    console.log(`Plugin loading completed. Loaded: ${Object.keys(pluginFeatures).length} plugins`);

  } catch (error) {
    console.error('Error reading plugins directory:', error);
  }
}

// 添加 IPC 处理程序以获取插件列表
ipcMain.handle('get-plugins', async (): Promise<PluginInfo[]> => {
  try {
    const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');
    if (!fs.existsSync(pluginsDir)) return [];

    const pluginFolders = fs.readdirSync(pluginsDir);
    const plugins: PluginInfo[] = [];

    for (const folder of pluginFolders) {
      const pluginPath = path.join(pluginsDir, folder);

      // 跳过非目录文件
      if (!fs.statSync(pluginPath).isDirectory()) {
        continue;
      }

      const manifestPath = path.join(pluginPath, 'manifest.json');

      try {
        if (fs.existsSync(manifestPath)) {
          const manifestContent = fs.readFileSync(manifestPath, 'utf-8');
          const manifest = JSON.parse(manifestContent) as PluginManifest;

          const loadStatus = pluginLoadStatus.get(manifest.name || folder);

          plugins.push({
            name: manifest.name || folder,
            description: manifest.description || 'No description',
            version: manifest.version || '1.0.0',
            status: loadStatus?.status || 'unknown',
            error: loadStatus?.error,
            author: manifest.author,
            license: manifest.license
          });
        }
      } catch (error) {
        // 如果单个插件解析失败，记录错误但继续处理其他插件
        console.error(`Error parsing plugin ${folder}:`, error);
        plugins.push({
          name: folder,
          description: 'Error loading plugin',
          version: 'unknown',
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return plugins;
  } catch (error) {
    console.error('Error getting plugins list:', error);
    throw new Error('Failed to get plugins list');
  }
});

// 添加插件安装功能
ipcMain.handle('install-plugin', async (_, pluginPath: string): Promise<PluginOperationResult> => {
  try {
    if (!pluginPath || typeof pluginPath !== 'string') {
      throw new Error('Invalid plugin path provided');
    }

    if (!fs.existsSync(pluginPath)) {
      throw new Error('Plugin file does not exist');
    }

    // 验证插件文件安全性
    const fileValidation = await securityManager.validatePluginFile(pluginPath);
    if (!fileValidation.valid) {
      throw new Error(`Plugin security validation failed: ${fileValidation.errors.join(', ')}`);
    }

    const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');
    if (!fs.existsSync(pluginsDir)) {
      fs.mkdirSync(pluginsDir, { recursive: true });
    }

    const pluginName = path.basename(pluginPath, path.extname(pluginPath));
    const targetDir = path.join(pluginsDir, pluginName);

    if (fs.existsSync(targetDir)) {
      throw new Error(`Plugin "${pluginName}" already exists`);
    }

    // 创建插件目录
    fs.mkdirSync(targetDir, { recursive: true });

    try {
      // 复制插件文件
      fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));

      // 尝试重新加载插件
      await loadPlugins();

      return {
        success: true,
        message: `Plugin "${pluginName}" installed successfully`
      };
    } catch (copyError) {
      // 如果复制失败，清理已创建的目录
      if (fs.existsSync(targetDir)) {
        fs.rmSync(targetDir, { recursive: true, force: true });
      }
      throw copyError;
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown installation error';
    console.error('Plugin installation failed:', errorMessage);
    throw new Error(`Installation failed: ${errorMessage}`);
  }
});

// 添加插件卸载功能
ipcMain.handle('uninstall-plugin', async (_, pluginName: string): Promise<PluginOperationResult> => {
  try {
    if (!pluginName || typeof pluginName !== 'string') {
      throw new Error('Invalid plugin name provided');
    }

    const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');
    const targetDir = path.join(pluginsDir, pluginName);

    if (!fs.existsSync(targetDir)) {
      throw new Error(`Plugin "${pluginName}" not found`);
    }

    // 检查是否为目录
    if (!fs.statSync(targetDir).isDirectory()) {
      throw new Error(`"${pluginName}" is not a valid plugin directory`);
    }

    // 尝试调用插件的deactivate函数（如果存在）
    try {
      if (pluginFeatures[pluginName]) {
        const manifestPath = path.join(targetDir, 'manifest.json');
        if (fs.existsSync(manifestPath)) {
          const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8')) as PluginManifest;
          const pluginEntry = path.join(targetDir, manifest.main);
          if (fs.existsSync(pluginEntry)) {
            const pluginEntryUrl = `file://${pluginEntry.replace(/\\/g, '/')}`;
            const plugin = await import(pluginEntryUrl) as PluginModule;
            if (typeof plugin.deactivate === 'function') {
              await plugin.deactivate();
            }
          }
        }
        // 从内存中移除插件
        delete pluginFeatures[pluginName];
        pluginLoadStatus.delete(pluginName);
      }
    } catch (deactivateError) {
      console.warn(`Warning: Failed to properly deactivate plugin "${pluginName}":`, deactivateError);
      // 继续卸载，即使deactivate失败
    }

    // 删除插件目录
    fs.rmSync(targetDir, { recursive: true, force: true });

    return {
      success: true,
      message: `Plugin "${pluginName}" uninstalled successfully`
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown uninstallation error';
    console.error('Plugin uninstallation failed:', errorMessage);
    throw new Error(`Uninstallation failed: ${errorMessage}`);
  }
});

// 执行插件功能（直接执行，不返回函数）
ipcMain.handle('execute-plugin-feature', async (_, pluginName: string, featureName: string, ...args: any[]) => {
  try {
    if (!pluginName || !featureName) {
      throw new Error('Plugin name and feature name are required');
    }

    if (!pluginFeatures[pluginName]) {
      throw new Error(`Plugin "${pluginName}" not found or not loaded`);
    }

    if (!pluginFeatures[pluginName][featureName]) {
      const availableFeatures = Object.keys(pluginFeatures[pluginName]);
      throw new Error(`Feature "${featureName}" not found in plugin "${pluginName}". Available features: ${availableFeatures.join(', ')}`);
    }

    const feature = pluginFeatures[pluginName][featureName];

    if (typeof feature !== 'function') {
      throw new Error(`Feature "${featureName}" in plugin "${pluginName}" is not a function`);
    }

    // 直接执行功能并返回结果
    const result = await feature(...args);
    return { success: true, result };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error executing plugin feature';
    console.error('Plugin feature execution failed:', errorMessage);
    return { success: false, error: errorMessage };
  }
});

// 添加获取插件加载状态的处理程序
ipcMain.handle('get-plugin-status', () => {
  const statusArray = Array.from(pluginLoadStatus.values());
  return statusArray;
});

// 安全管理相关的IPC处理程序
ipcMain.handle('get-security-policy', () => {
  return securityManager.getPolicy();
});

ipcMain.handle('update-security-policy', (_, newPolicy) => {
  securityManager.updatePolicy(newPolicy);
  return { success: true, message: 'Security policy updated successfully' };
});

ipcMain.handle('add-trusted-plugin', (_, pluginName: string) => {
  securityManager.addTrustedPlugin(pluginName);
  return { success: true, message: `Plugin "${pluginName}" added to trusted list` };
});

ipcMain.handle('remove-trusted-plugin', (_, pluginName: string) => {
  securityManager.removeTrustedPlugin(pluginName);
  return { success: true, message: `Plugin "${pluginName}" removed from trusted list` };
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
    win = null
  }
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

app.whenReady().then(async () => {
  createWindow()
  await loadPlugins();
})
