import { app, BrowserWindow, ipcMain } from 'electron'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import fs from 'fs'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.mjs
// │
process.env.APP_ROOT = path.join(__dirname, '..')

// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST

let win: BrowserWindow | null

function createWindow() {
  win = new BrowserWindow({
    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.mjs'),
    },
  })

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())
  })

  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL)
  } else {
    // win.loadFile('dist/index.html')
    win.loadFile(path.join(RENDERER_DIST, 'index.html'))
  }
}

// 为 pluginFeatures 定义类型
interface PluginFeatures {
  [pluginName: string]: {
    [featureName: string]: (...args: any[]) => any;
  };
}

const pluginFeatures: PluginFeatures = {};

function loadPlugins() {
  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');
  if (!fs.existsSync(pluginsDir)) return;

  const pluginFolders = fs.readdirSync(pluginsDir);
  pluginFolders.forEach(folder => {
    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');
    if (fs.existsSync(manifestPath)) {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));
      const pluginEntry = path.join(pluginsDir, folder, manifest.main);
      if (fs.existsSync(pluginEntry)) {
        import(pluginEntry).then(plugin => {
          if (plugin.activate) {
            const features = plugin.activate();
            if (features) {
              pluginFeatures[manifest.name] = features;
            }
          }
        }).catch(err => console.error(`Failed to load plugin ${folder}:`, err));
      }
    }
  });
}

// 添加 IPC 处理程序以获取插件列表
ipcMain.handle('get-plugins', () => {
  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');
  if (!fs.existsSync(pluginsDir)) return [];

  const pluginFolders = fs.readdirSync(pluginsDir);
  return pluginFolders.map(folder => {
    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');
    if (fs.existsSync(manifestPath)) {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));
      return {
        name: manifest.name,
        description: manifest.description,
      };
    }
    return null;
  }).filter(Boolean);
});

// 添加插件安装功能
ipcMain.handle('install-plugin', (_, pluginPath) => {
  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');
  if (!fs.existsSync(pluginsDir)) {
    fs.mkdirSync(pluginsDir);
  }

  const pluginName = path.basename(pluginPath, path.extname(pluginPath));
  const targetDir = path.join(pluginsDir, pluginName);

  if (fs.existsSync(targetDir)) {
    throw new Error('Plugin already exists');
  }

  fs.mkdirSync(targetDir);
  fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));
  return true;
});

// 添加插件卸载功能
ipcMain.handle('uninstall-plugin', (_, pluginName) => {
  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');
  const targetDir = path.join(pluginsDir, pluginName);

  if (fs.existsSync(targetDir)) {
    fs.rmSync(targetDir, { recursive: true, force: true });
    return true;
  }

  throw new Error('Plugin not found');
});

ipcMain.handle('get-plugin-feature', (_, pluginName, featureName) => {
  if (pluginFeatures[pluginName] && pluginFeatures[pluginName][featureName]) {
    return pluginFeatures[pluginName][featureName];
  }
  throw new Error(`Feature ${featureName} not found in plugin ${pluginName}`);
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
    win = null
  }
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

app.whenReady().then(() => {
  createWindow()
  loadPlugins();
})
