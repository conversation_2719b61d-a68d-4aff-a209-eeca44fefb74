{"sourceFile": "electron/main.ts", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1750233236480, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750233255852, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,154 @@\n+import { app, BrowserWindow, ipcMain } from 'electron'\r\n+import { fileURLToPath } from 'node:url'\r\n+import path from 'node:path'\r\n+import fs from 'fs'\r\n+\r\n+const __dirname = path.dirname(fileURLToPath(import.meta.url))\r\n+\r\n+// The built directory structure\r\n+//\r\n+// ├─┬─┬ dist\r\n+// │ │ └── index.html\r\n+// │ │\r\n+// │ ├─┬ dist-electron\r\n+// │ │ ├── main.js\r\n+// │ │ └── preload.mjs\r\n+// │\r\n+process.env.APP_ROOT = path.join(__dirname, '..')\r\n+\r\n+// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x\r\n+export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']\r\n+export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')\r\n+export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')\r\n+\r\n+process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST\r\n+\r\n+let win: BrowserWindow | null\r\n+\r\n+function createWindow() {\r\n+  win = new BrowserWindow({\r\n+    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),\r\n+    webPreferences: {\r\n+      preload: path.join(__dirname, 'preload.mjs'),\r\n+    },\r\n+  })\r\n+\r\n+  // Test active push message to Renderer-process.\r\n+  win.webContents.on('did-finish-load', () => {\r\n+    win?.webContents.send('main-process-message', (new Date).toLocaleString())\r\n+  })\r\n+\r\n+  if (VITE_DEV_SERVER_URL) {\r\n+    win.loadURL(VITE_DEV_SERVER_URL)\r\n+  } else {\r\n+    // win.loadFile('dist/index.html')\r\n+    win.loadFile(path.join(RENDERER_DIST, 'index.html'))\r\n+  }\r\n+}\r\n+\r\n+const pluginFeatures = {};\r\n+\r\n+function loadPlugins() {\r\n+  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n+  if (!fs.existsSync(pluginsDir)) return;\r\n+\r\n+  const pluginFolders = fs.readdirSync(pluginsDir);\r\n+  pluginFolders.forEach(folder => {\r\n+    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n+    if (fs.existsSync(manifestPath)) {\r\n+      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n+      const pluginEntry = path.join(pluginsDir, folder, manifest.main);\r\n+      if (fs.existsSync(pluginEntry)) {\r\n+        import(pluginEntry).then(plugin => {\r\n+          if (plugin.activate) {\r\n+            const features = plugin.activate();\r\n+            if (features) {\r\n+              pluginFeatures[manifest.name] = features;\r\n+            }\r\n+          }\r\n+        }).catch(err => console.error(`Failed to load plugin ${folder}:`, err));\r\n+      }\r\n+    }\r\n+  });\r\n+}\r\n+\r\n+// 添加 IPC 处理程序以获取插件列表\r\n+ipcMain.handle('get-plugins', () => {\r\n+  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n+  if (!fs.existsSync(pluginsDir)) return [];\r\n+\r\n+  const pluginFolders = fs.readdirSync(pluginsDir);\r\n+  return pluginFolders.map(folder => {\r\n+    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n+    if (fs.existsSync(manifestPath)) {\r\n+      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n+      return {\r\n+        name: manifest.name,\r\n+        description: manifest.description,\r\n+      };\r\n+    }\r\n+    return null;\r\n+  }).filter(Boolean);\r\n+});\r\n+\r\n+// 添加插件安装功能\r\n+ipcMain.handle('install-plugin', (_, pluginPath) => {\r\n+  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n+  if (!fs.existsSync(pluginsDir)) {\r\n+    fs.mkdirSync(pluginsDir);\r\n+  }\r\n+\r\n+  const pluginName = path.basename(pluginPath, path.extname(pluginPath));\r\n+  const targetDir = path.join(pluginsDir, pluginName);\r\n+\r\n+  if (fs.existsSync(targetDir)) {\r\n+    throw new Error('Plugin already exists');\r\n+  }\r\n+\r\n+  fs.mkdirSync(targetDir);\r\n+  fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));\r\n+  return true;\r\n+});\r\n+\r\n+// 添加插件卸载功能\r\n+ipcMain.handle('uninstall-plugin', (_, pluginName) => {\r\n+  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n+  const targetDir = path.join(pluginsDir, pluginName);\r\n+\r\n+  if (fs.existsSync(targetDir)) {\r\n+    fs.rmSync(targetDir, { recursive: true, force: true });\r\n+    return true;\r\n+  }\r\n+\r\n+  throw new Error('Plugin not found');\r\n+});\r\n+\r\n+ipcMain.handle('get-plugin-feature', (event, pluginName, featureName) => {\r\n+  if (pluginFeatures[pluginName] && pluginFeatures[pluginName][featureName]) {\r\n+    return pluginFeatures[pluginName][featureName];\r\n+  }\r\n+  throw new Error(`Feature ${featureName} not found in plugin ${pluginName}`);\r\n+});\r\n+\r\n+// Quit when all windows are closed, except on macOS. There, it's common\r\n+// for applications and their menu bar to stay active until the user quits\r\n+// explicitly with Cmd + Q.\r\n+app.on('window-all-closed', () => {\r\n+  if (process.platform !== 'darwin') {\r\n+    app.quit()\r\n+    win = null\r\n+  }\r\n+})\r\n+\r\n+app.on('activate', () => {\r\n+  // On OS X it's common to re-create a window in the app when the\r\n+  // dock icon is clicked and there are no other windows open.\r\n+  if (BrowserWindow.getAllWindows().length === 0) {\r\n+    createWindow()\r\n+  }\r\n+})\r\n+\r\n+app.whenReady().then(() => {\r\n+  createWindow()\r\n+  loadPlugins();\r\n+})\r\n"}, {"date": 1750233312549, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -45,10 +45,17 @@\n     win.loadFile(path.join(RENDERER_DIST, 'index.html'))\r\n   }\r\n }\r\n \r\n-const pluginFeatures = {};\r\n+// 为 pluginFeatures 定义类型\r\n+interface PluginFeatures {\r\n+  [pluginName: string]: {\r\n+    [featureName: string]: (...args: any[]) => any;\r\n+  };\r\n+}\r\n \r\n+const pluginFeatures: PluginFeatures = {};\r\n+\r\n function loadPlugins() {\r\n   const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n   if (!fs.existsSync(pluginsDir)) return;\r\n \r\n@@ -122,9 +129,9 @@\n \r\n   throw new Error('Plugin not found');\r\n });\r\n \r\n-ipcMain.handle('get-plugin-feature', (event, pluginName, featureName) => {\r\n+ipcMain.handle('get-plugin-feature', (_, pluginName, featureName) => {\r\n   if (pluginFeatures[pluginName] && pluginFeatures[pluginName][featureName]) {\r\n     return pluginFeatures[pluginName][featureName];\r\n   }\r\n   throw new Error(`Feature ${featureName} not found in plugin ${pluginName}`);\r\n@@ -151,151 +158,4 @@\n app.whenReady().then(() => {\r\n   createWindow()\r\n   loadPlugins();\r\n })\r\n-import { app, BrowserWindow, ipcMain } from 'electron'\r\n-import { fileURLToPath } from 'node:url'\r\n-import path from 'node:path'\r\n-import fs from 'fs'\r\n-\r\n-const __dirname = path.dirname(fileURLToPath(import.meta.url))\r\n-\r\n-// The built directory structure\r\n-//\r\n-// ├─┬─┬ dist\r\n-// │ │ └── index.html\r\n-// │ │\r\n-// │ ├─┬ dist-electron\r\n-// │ │ ├── main.js\r\n-// │ │ └── preload.mjs\r\n-// │\r\n-process.env.APP_ROOT = path.join(__dirname, '..')\r\n-\r\n-// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x\r\n-export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']\r\n-export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')\r\n-export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')\r\n-\r\n-process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST\r\n-\r\n-let win: BrowserWindow | null\r\n-\r\n-function createWindow() {\r\n-  win = new BrowserWindow({\r\n-    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),\r\n-    webPreferences: {\r\n-      preload: path.join(__dirname, 'preload.mjs'),\r\n-    },\r\n-  })\r\n-\r\n-  // Test active push message to Renderer-process.\r\n-  win.webContents.on('did-finish-load', () => {\r\n-    win?.webContents.send('main-process-message', (new Date).toLocaleString())\r\n-  })\r\n-\r\n-  if (VITE_DEV_SERVER_URL) {\r\n-    win.loadURL(VITE_DEV_SERVER_URL)\r\n-  } else {\r\n-    // win.loadFile('dist/index.html')\r\n-    win.loadFile(path.join(RENDERER_DIST, 'index.html'))\r\n-  }\r\n-}\r\n-\r\n-const pluginFeatures = {};\r\n-\r\n-function loadPlugins() {\r\n-  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  if (!fs.existsSync(pluginsDir)) return;\r\n-\r\n-  const pluginFolders = fs.readdirSync(pluginsDir);\r\n-  pluginFolders.forEach(folder => {\r\n-    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n-    if (fs.existsSync(manifestPath)) {\r\n-      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n-      const pluginEntry = path.join(pluginsDir, folder, manifest.main);\r\n-      if (fs.existsSync(pluginEntry)) {\r\n-        import(pluginEntry).then(plugin => {\r\n-          if (plugin.activate) {\r\n-            const features = plugin.activate();\r\n-            if (features) {\r\n-              pluginFeatures[manifest.name] = features;\r\n-            }\r\n-          }\r\n-        }).catch(err => console.error(`Failed to load plugin ${folder}:`, err));\r\n-      }\r\n-    }\r\n-  });\r\n-}\r\n-\r\n-// 添加 IPC 处理程序以获取插件列表\r\n-ipcMain.handle('get-plugins', () => {\r\n-  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  if (!fs.existsSync(pluginsDir)) return [];\r\n-\r\n-  const pluginFolders = fs.readdirSync(pluginsDir);\r\n-  return pluginFolders.map(folder => {\r\n-    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n-    if (fs.existsSync(manifestPath)) {\r\n-      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n-      return {\r\n-        name: manifest.name,\r\n-        description: manifest.description,\r\n-      };\r\n-    }\r\n-    return null;\r\n-  }).filter(Boolean);\r\n-});\r\n-\r\n-// 添加插件安装功能\r\n-ipcMain.handle('install-plugin', (_, pluginPath) => {\r\n-  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  if (!fs.existsSync(pluginsDir)) {\r\n-    fs.mkdirSync(pluginsDir);\r\n-  }\r\n-\r\n-  const pluginName = path.basename(pluginPath, path.extname(pluginPath));\r\n-  const targetDir = path.join(pluginsDir, pluginName);\r\n-\r\n-  if (fs.existsSync(targetDir)) {\r\n-    throw new Error('Plugin already exists');\r\n-  }\r\n-\r\n-  fs.mkdirSync(targetDir);\r\n-  fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));\r\n-  return true;\r\n-});\r\n-\r\n-// 添加插件卸载功能\r\n-ipcMain.handle('uninstall-plugin', (_, pluginName) => {\r\n-  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  const targetDir = path.join(pluginsDir, pluginName);\r\n-\r\n-  if (fs.existsSync(targetDir)) {\r\n-    fs.rmSync(targetDir, { recursive: true, force: true });\r\n-    return true;\r\n-  }\r\n-\r\n-  throw new Error('Plugin not found');\r\n-});\r\n-\r\n-// Quit when all windows are closed, except on macOS. There, it's common\r\n-// for applications and their menu bar to stay active until the user quits\r\n-// explicitly with Cmd + Q.\r\n-app.on('window-all-closed', () => {\r\n-  if (process.platform !== 'darwin') {\r\n-    app.quit()\r\n-    win = null\r\n-  }\r\n-})\r\n-\r\n-app.on('activate', () => {\r\n-  // On OS X it's common to re-create a window in the app when the\r\n-  // dock icon is clicked and there are no other windows open.\r\n-  if (BrowserWindow.getAllWindows().length === 0) {\r\n-    createWindow()\r\n-  }\r\n-})\r\n-\r\n-app.whenReady().then(() => {\r\n-  createWindow()\r\n-  loadPlugins();\r\n-})\r\n"}], "date": 1750233236480, "name": "Commit-0", "content": "import { app, BrowserWindow, ipcMain } from 'electron'\r\nimport { fileURLToPath } from 'node:url'\r\nimport path from 'node:path'\r\nimport fs from 'fs'\r\n\r\nconst __dirname = path.dirname(fileURLToPath(import.meta.url))\r\n\r\n// The built directory structure\r\n//\r\n// ├─┬─┬ dist\r\n// │ │ └── index.html\r\n// │ │\r\n// │ ├─┬ dist-electron\r\n// │ │ ├── main.js\r\n// │ │ └── preload.mjs\r\n// │\r\nprocess.env.APP_ROOT = path.join(__dirname, '..')\r\n\r\n// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x\r\nexport const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']\r\nexport const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')\r\nexport const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')\r\n\r\nprocess.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST\r\n\r\nlet win: BrowserWindow | null\r\n\r\nfunction createWindow() {\r\n  win = new BrowserWindow({\r\n    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),\r\n    webPreferences: {\r\n      preload: path.join(__dirname, 'preload.mjs'),\r\n    },\r\n  })\r\n\r\n  // Test active push message to Renderer-process.\r\n  win.webContents.on('did-finish-load', () => {\r\n    win?.webContents.send('main-process-message', (new Date).toLocaleString())\r\n  })\r\n\r\n  if (VITE_DEV_SERVER_URL) {\r\n    win.loadURL(VITE_DEV_SERVER_URL)\r\n  } else {\r\n    // win.loadFile('dist/index.html')\r\n    win.loadFile(path.join(RENDERER_DIST, 'index.html'))\r\n  }\r\n}\r\n\r\nconst pluginFeatures = {};\r\n\r\nfunction loadPlugins() {\r\n  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n  if (!fs.existsSync(pluginsDir)) return;\r\n\r\n  const pluginFolders = fs.readdirSync(pluginsDir);\r\n  pluginFolders.forEach(folder => {\r\n    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n    if (fs.existsSync(manifestPath)) {\r\n      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n      const pluginEntry = path.join(pluginsDir, folder, manifest.main);\r\n      if (fs.existsSync(pluginEntry)) {\r\n        import(pluginEntry).then(plugin => {\r\n          if (plugin.activate) {\r\n            const features = plugin.activate();\r\n            if (features) {\r\n              pluginFeatures[manifest.name] = features;\r\n            }\r\n          }\r\n        }).catch(err => console.error(`Failed to load plugin ${folder}:`, err));\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\n// 添加 IPC 处理程序以获取插件列表\r\nipcMain.handle('get-plugins', () => {\r\n  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n  if (!fs.existsSync(pluginsDir)) return [];\r\n\r\n  const pluginFolders = fs.readdirSync(pluginsDir);\r\n  return pluginFolders.map(folder => {\r\n    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n    if (fs.existsSync(manifestPath)) {\r\n      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n      return {\r\n        name: manifest.name,\r\n        description: manifest.description,\r\n      };\r\n    }\r\n    return null;\r\n  }).filter(Boolean);\r\n});\r\n\r\n// 添加插件安装功能\r\nipcMain.handle('install-plugin', (_, pluginPath) => {\r\n  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n  if (!fs.existsSync(pluginsDir)) {\r\n    fs.mkdirSync(pluginsDir);\r\n  }\r\n\r\n  const pluginName = path.basename(pluginPath, path.extname(pluginPath));\r\n  const targetDir = path.join(pluginsDir, pluginName);\r\n\r\n  if (fs.existsSync(targetDir)) {\r\n    throw new Error('Plugin already exists');\r\n  }\r\n\r\n  fs.mkdirSync(targetDir);\r\n  fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));\r\n  return true;\r\n});\r\n\r\n// 添加插件卸载功能\r\nipcMain.handle('uninstall-plugin', (_, pluginName) => {\r\n  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n  const targetDir = path.join(pluginsDir, pluginName);\r\n\r\n  if (fs.existsSync(targetDir)) {\r\n    fs.rmSync(targetDir, { recursive: true, force: true });\r\n    return true;\r\n  }\r\n\r\n  throw new Error('Plugin not found');\r\n});\r\n\r\n// Quit when all windows are closed, except on macOS. There, it's common\r\n// for applications and their menu bar to stay active until the user quits\r\n// explicitly with Cmd + Q.\r\napp.on('window-all-closed', () => {\r\n  if (process.platform !== 'darwin') {\r\n    app.quit()\r\n    win = null\r\n  }\r\n})\r\n\r\napp.on('activate', () => {\r\n  // On OS X it's common to re-create a window in the app when the\r\n  // dock icon is clicked and there are no other windows open.\r\n  if (BrowserWindow.getAllWindows().length === 0) {\r\n    createWindow()\r\n  }\r\n})\r\n\r\napp.whenReady().then(() => {\r\n  createWindow()\r\n  loadPlugins();\r\n})\r\n"}]}