{"sourceFile": "electron/main.ts", "activeCommit": 0, "commits": [{"activePatchIndex": 22, "patches": [{"date": 1750233236480, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750233255852, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,154 @@\n+import { app, BrowserWindow, ipcMain } from 'electron'\r\n+import { fileURLToPath } from 'node:url'\r\n+import path from 'node:path'\r\n+import fs from 'fs'\r\n+\r\n+const __dirname = path.dirname(fileURLToPath(import.meta.url))\r\n+\r\n+// The built directory structure\r\n+//\r\n+// ├─┬─┬ dist\r\n+// │ │ └── index.html\r\n+// │ │\r\n+// │ ├─┬ dist-electron\r\n+// │ │ ├── main.js\r\n+// │ │ └── preload.mjs\r\n+// │\r\n+process.env.APP_ROOT = path.join(__dirname, '..')\r\n+\r\n+// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x\r\n+export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']\r\n+export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')\r\n+export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')\r\n+\r\n+process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST\r\n+\r\n+let win: BrowserWindow | null\r\n+\r\n+function createWindow() {\r\n+  win = new BrowserWindow({\r\n+    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),\r\n+    webPreferences: {\r\n+      preload: path.join(__dirname, 'preload.mjs'),\r\n+    },\r\n+  })\r\n+\r\n+  // Test active push message to Renderer-process.\r\n+  win.webContents.on('did-finish-load', () => {\r\n+    win?.webContents.send('main-process-message', (new Date).toLocaleString())\r\n+  })\r\n+\r\n+  if (VITE_DEV_SERVER_URL) {\r\n+    win.loadURL(VITE_DEV_SERVER_URL)\r\n+  } else {\r\n+    // win.loadFile('dist/index.html')\r\n+    win.loadFile(path.join(RENDERER_DIST, 'index.html'))\r\n+  }\r\n+}\r\n+\r\n+const pluginFeatures = {};\r\n+\r\n+function loadPlugins() {\r\n+  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n+  if (!fs.existsSync(pluginsDir)) return;\r\n+\r\n+  const pluginFolders = fs.readdirSync(pluginsDir);\r\n+  pluginFolders.forEach(folder => {\r\n+    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n+    if (fs.existsSync(manifestPath)) {\r\n+      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n+      const pluginEntry = path.join(pluginsDir, folder, manifest.main);\r\n+      if (fs.existsSync(pluginEntry)) {\r\n+        import(pluginEntry).then(plugin => {\r\n+          if (plugin.activate) {\r\n+            const features = plugin.activate();\r\n+            if (features) {\r\n+              pluginFeatures[manifest.name] = features;\r\n+            }\r\n+          }\r\n+        }).catch(err => console.error(`Failed to load plugin ${folder}:`, err));\r\n+      }\r\n+    }\r\n+  });\r\n+}\r\n+\r\n+// 添加 IPC 处理程序以获取插件列表\r\n+ipcMain.handle('get-plugins', () => {\r\n+  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n+  if (!fs.existsSync(pluginsDir)) return [];\r\n+\r\n+  const pluginFolders = fs.readdirSync(pluginsDir);\r\n+  return pluginFolders.map(folder => {\r\n+    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n+    if (fs.existsSync(manifestPath)) {\r\n+      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n+      return {\r\n+        name: manifest.name,\r\n+        description: manifest.description,\r\n+      };\r\n+    }\r\n+    return null;\r\n+  }).filter(Boolean);\r\n+});\r\n+\r\n+// 添加插件安装功能\r\n+ipcMain.handle('install-plugin', (_, pluginPath) => {\r\n+  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n+  if (!fs.existsSync(pluginsDir)) {\r\n+    fs.mkdirSync(pluginsDir);\r\n+  }\r\n+\r\n+  const pluginName = path.basename(pluginPath, path.extname(pluginPath));\r\n+  const targetDir = path.join(pluginsDir, pluginName);\r\n+\r\n+  if (fs.existsSync(targetDir)) {\r\n+    throw new Error('Plugin already exists');\r\n+  }\r\n+\r\n+  fs.mkdirSync(targetDir);\r\n+  fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));\r\n+  return true;\r\n+});\r\n+\r\n+// 添加插件卸载功能\r\n+ipcMain.handle('uninstall-plugin', (_, pluginName) => {\r\n+  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n+  const targetDir = path.join(pluginsDir, pluginName);\r\n+\r\n+  if (fs.existsSync(targetDir)) {\r\n+    fs.rmSync(targetDir, { recursive: true, force: true });\r\n+    return true;\r\n+  }\r\n+\r\n+  throw new Error('Plugin not found');\r\n+});\r\n+\r\n+ipcMain.handle('get-plugin-feature', (event, pluginName, featureName) => {\r\n+  if (pluginFeatures[pluginName] && pluginFeatures[pluginName][featureName]) {\r\n+    return pluginFeatures[pluginName][featureName];\r\n+  }\r\n+  throw new Error(`Feature ${featureName} not found in plugin ${pluginName}`);\r\n+});\r\n+\r\n+// Quit when all windows are closed, except on macOS. There, it's common\r\n+// for applications and their menu bar to stay active until the user quits\r\n+// explicitly with Cmd + Q.\r\n+app.on('window-all-closed', () => {\r\n+  if (process.platform !== 'darwin') {\r\n+    app.quit()\r\n+    win = null\r\n+  }\r\n+})\r\n+\r\n+app.on('activate', () => {\r\n+  // On OS X it's common to re-create a window in the app when the\r\n+  // dock icon is clicked and there are no other windows open.\r\n+  if (BrowserWindow.getAllWindows().length === 0) {\r\n+    createWindow()\r\n+  }\r\n+})\r\n+\r\n+app.whenReady().then(() => {\r\n+  createWindow()\r\n+  loadPlugins();\r\n+})\r\n"}, {"date": 1750233312549, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -45,10 +45,17 @@\n     win.loadFile(path.join(RENDERER_DIST, 'index.html'))\r\n   }\r\n }\r\n \r\n-const pluginFeatures = {};\r\n+// 为 pluginFeatures 定义类型\r\n+interface PluginFeatures {\r\n+  [pluginName: string]: {\r\n+    [featureName: string]: (...args: any[]) => any;\r\n+  };\r\n+}\r\n \r\n+const pluginFeatures: PluginFeatures = {};\r\n+\r\n function loadPlugins() {\r\n   const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n   if (!fs.existsSync(pluginsDir)) return;\r\n \r\n@@ -122,9 +129,9 @@\n \r\n   throw new Error('Plugin not found');\r\n });\r\n \r\n-ipcMain.handle('get-plugin-feature', (event, pluginName, featureName) => {\r\n+ipcMain.handle('get-plugin-feature', (_, pluginName, featureName) => {\r\n   if (pluginFeatures[pluginName] && pluginFeatures[pluginName][featureName]) {\r\n     return pluginFeatures[pluginName][featureName];\r\n   }\r\n   throw new Error(`Feature ${featureName} not found in plugin ${pluginName}`);\r\n@@ -151,151 +158,4 @@\n app.whenReady().then(() => {\r\n   createWindow()\r\n   loadPlugins();\r\n })\r\n-import { app, BrowserWindow, ipcMain } from 'electron'\r\n-import { fileURLToPath } from 'node:url'\r\n-import path from 'node:path'\r\n-import fs from 'fs'\r\n-\r\n-const __dirname = path.dirname(fileURLToPath(import.meta.url))\r\n-\r\n-// The built directory structure\r\n-//\r\n-// ├─┬─┬ dist\r\n-// │ │ └── index.html\r\n-// │ │\r\n-// │ ├─┬ dist-electron\r\n-// │ │ ├── main.js\r\n-// │ │ └── preload.mjs\r\n-// │\r\n-process.env.APP_ROOT = path.join(__dirname, '..')\r\n-\r\n-// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x\r\n-export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']\r\n-export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')\r\n-export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')\r\n-\r\n-process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST\r\n-\r\n-let win: BrowserWindow | null\r\n-\r\n-function createWindow() {\r\n-  win = new BrowserWindow({\r\n-    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),\r\n-    webPreferences: {\r\n-      preload: path.join(__dirname, 'preload.mjs'),\r\n-    },\r\n-  })\r\n-\r\n-  // Test active push message to Renderer-process.\r\n-  win.webContents.on('did-finish-load', () => {\r\n-    win?.webContents.send('main-process-message', (new Date).toLocaleString())\r\n-  })\r\n-\r\n-  if (VITE_DEV_SERVER_URL) {\r\n-    win.loadURL(VITE_DEV_SERVER_URL)\r\n-  } else {\r\n-    // win.loadFile('dist/index.html')\r\n-    win.loadFile(path.join(RENDERER_DIST, 'index.html'))\r\n-  }\r\n-}\r\n-\r\n-const pluginFeatures = {};\r\n-\r\n-function loadPlugins() {\r\n-  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  if (!fs.existsSync(pluginsDir)) return;\r\n-\r\n-  const pluginFolders = fs.readdirSync(pluginsDir);\r\n-  pluginFolders.forEach(folder => {\r\n-    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n-    if (fs.existsSync(manifestPath)) {\r\n-      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n-      const pluginEntry = path.join(pluginsDir, folder, manifest.main);\r\n-      if (fs.existsSync(pluginEntry)) {\r\n-        import(pluginEntry).then(plugin => {\r\n-          if (plugin.activate) {\r\n-            const features = plugin.activate();\r\n-            if (features) {\r\n-              pluginFeatures[manifest.name] = features;\r\n-            }\r\n-          }\r\n-        }).catch(err => console.error(`Failed to load plugin ${folder}:`, err));\r\n-      }\r\n-    }\r\n-  });\r\n-}\r\n-\r\n-// 添加 IPC 处理程序以获取插件列表\r\n-ipcMain.handle('get-plugins', () => {\r\n-  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  if (!fs.existsSync(pluginsDir)) return [];\r\n-\r\n-  const pluginFolders = fs.readdirSync(pluginsDir);\r\n-  return pluginFolders.map(folder => {\r\n-    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n-    if (fs.existsSync(manifestPath)) {\r\n-      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n-      return {\r\n-        name: manifest.name,\r\n-        description: manifest.description,\r\n-      };\r\n-    }\r\n-    return null;\r\n-  }).filter(Boolean);\r\n-});\r\n-\r\n-// 添加插件安装功能\r\n-ipcMain.handle('install-plugin', (_, pluginPath) => {\r\n-  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  if (!fs.existsSync(pluginsDir)) {\r\n-    fs.mkdirSync(pluginsDir);\r\n-  }\r\n-\r\n-  const pluginName = path.basename(pluginPath, path.extname(pluginPath));\r\n-  const targetDir = path.join(pluginsDir, pluginName);\r\n-\r\n-  if (fs.existsSync(targetDir)) {\r\n-    throw new Error('Plugin already exists');\r\n-  }\r\n-\r\n-  fs.mkdirSync(targetDir);\r\n-  fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));\r\n-  return true;\r\n-});\r\n-\r\n-// 添加插件卸载功能\r\n-ipcMain.handle('uninstall-plugin', (_, pluginName) => {\r\n-  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  const targetDir = path.join(pluginsDir, pluginName);\r\n-\r\n-  if (fs.existsSync(targetDir)) {\r\n-    fs.rmSync(targetDir, { recursive: true, force: true });\r\n-    return true;\r\n-  }\r\n-\r\n-  throw new Error('Plugin not found');\r\n-});\r\n-\r\n-// Quit when all windows are closed, except on macOS. There, it's common\r\n-// for applications and their menu bar to stay active until the user quits\r\n-// explicitly with Cmd + Q.\r\n-app.on('window-all-closed', () => {\r\n-  if (process.platform !== 'darwin') {\r\n-    app.quit()\r\n-    win = null\r\n-  }\r\n-})\r\n-\r\n-app.on('activate', () => {\r\n-  // On OS X it's common to re-create a window in the app when the\r\n-  // dock icon is clicked and there are no other windows open.\r\n-  if (BrowserWindow.getAllWindows().length === 0) {\r\n-    createWindow()\r\n-  }\r\n-})\r\n-\r\n-app.whenReady().then(() => {\r\n-  createWindow()\r\n-  loadPlugins();\r\n-})\r\n"}, {"date": 1750234697022, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -54,30 +54,95 @@\n }\r\n \r\n const pluginFeatures: PluginFeatures = {};\r\n \r\n-function loadPlugins() {\r\n+// 插件加载状态跟踪\r\n+interface PluginLoadStatus {\r\n+  name: string;\r\n+  status: 'loading' | 'loaded' | 'failed';\r\n+  error?: string;\r\n+}\r\n+\r\n+const pluginLoadStatus: Map<string, PluginLoadStatus> = new Map();\r\n+\r\n+async function loadPlugins() {\r\n   const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  if (!fs.existsSync(pluginsDir)) return;\r\n+  if (!fs.existsSync(pluginsDir)) {\r\n+    console.log('Plugins directory not found, creating...');\r\n+    fs.mkdirSync(pluginsDir, { recursive: true });\r\n+    return;\r\n+  }\r\n \r\n-  const pluginFolders = fs.readdirSync(pluginsDir);\r\n-  pluginFolders.forEach(folder => {\r\n-    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n-    if (fs.existsSync(manifestPath)) {\r\n-      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n-      const pluginEntry = path.join(pluginsDir, folder, manifest.main);\r\n-      if (fs.existsSync(pluginEntry)) {\r\n-        import(pluginEntry).then(plugin => {\r\n-          if (plugin.activate) {\r\n-            const features = plugin.activate();\r\n-            if (features) {\r\n-              pluginFeatures[manifest.name] = features;\r\n-            }\r\n-          }\r\n-        }).catch(err => console.error(`Failed to load plugin ${folder}:`, err));\r\n+  try {\r\n+    const pluginFolders = fs.readdirSync(pluginsDir);\r\n+    const loadPromises = pluginFolders.map(async (folder) => {\r\n+      const pluginPath = path.join(pluginsDir, folder);\r\n+\r\n+      // 跳过非目录文件\r\n+      if (!fs.statSync(pluginPath).isDirectory()) {\r\n+        return;\r\n       }\r\n-    }\r\n-  });\r\n+\r\n+      const manifestPath = path.join(pluginPath, 'manifest.json');\r\n+\r\n+      try {\r\n+        if (!fs.existsSync(manifestPath)) {\r\n+          throw new Error(`Manifest file not found: ${manifestPath}`);\r\n+        }\r\n+\r\n+        const manifestContent = fs.readFileSync(manifestPath, 'utf-8');\r\n+        let manifest;\r\n+\r\n+        try {\r\n+          manifest = JSON.parse(manifestContent);\r\n+        } catch (parseError) {\r\n+          throw new Error(`Invalid JSON in manifest: ${parseError.message}`);\r\n+        }\r\n+\r\n+        // 验证必需字段\r\n+        if (!manifest.name || !manifest.main) {\r\n+          throw new Error('Manifest must contain \"name\" and \"main\" fields');\r\n+        }\r\n+\r\n+        pluginLoadStatus.set(manifest.name, { name: manifest.name, status: 'loading' });\r\n+\r\n+        const pluginEntry = path.join(pluginPath, manifest.main);\r\n+        if (!fs.existsSync(pluginEntry)) {\r\n+          throw new Error(`Plugin entry file not found: ${pluginEntry}`);\r\n+        }\r\n+\r\n+        const plugin = await import(pluginEntry);\r\n+\r\n+        if (typeof plugin.activate !== 'function') {\r\n+          throw new Error('Plugin must export an \"activate\" function');\r\n+        }\r\n+\r\n+        const features = plugin.activate();\r\n+        if (features && typeof features === 'object') {\r\n+          pluginFeatures[manifest.name] = features;\r\n+          pluginLoadStatus.set(manifest.name, { name: manifest.name, status: 'loaded' });\r\n+          console.log(`Plugin \"${manifest.name}\" loaded successfully`);\r\n+        } else {\r\n+          throw new Error('Plugin activate function must return an object with features');\r\n+        }\r\n+\r\n+      } catch (error) {\r\n+        const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n+        console.error(`Failed to load plugin \"${folder}\":`, errorMessage);\r\n+        pluginLoadStatus.set(folder, {\r\n+          name: folder,\r\n+          status: 'failed',\r\n+          error: errorMessage\r\n+        });\r\n+      }\r\n+    });\r\n+\r\n+    await Promise.allSettled(loadPromises);\r\n+    console.log(`Plugin loading completed. Loaded: ${Object.keys(pluginFeatures).length} plugins`);\r\n+\r\n+  } catch (error) {\r\n+    console.error('Error reading plugins directory:', error);\r\n+  }\r\n }\r\n \r\n // 添加 IPC 处理程序以获取插件列表\r\n ipcMain.handle('get-plugins', () => {\r\n"}, {"date": 1750234708117, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -94,9 +94,10 @@\n \r\n         try {\r\n           manifest = JSON.parse(manifestContent);\r\n         } catch (parseError) {\r\n-          throw new Error(`Invalid JSON in manifest: ${parseError.message}`);\r\n+          const errorMessage = parseError instanceof Error ? parseError.message : 'Invalid JSON format';\r\n+          throw new Error(`Invalid JSON in manifest: ${errorMessage}`);\r\n         }\r\n \r\n         // 验证必需字段\r\n         if (!manifest.name || !manifest.main) {\r\n"}, {"date": 1750234728470, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -145,24 +145,59 @@\n   }\r\n }\r\n \r\n // 添加 IPC 处理程序以获取插件列表\r\n-ipcMain.handle('get-plugins', () => {\r\n-  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  if (!fs.existsSync(pluginsDir)) return [];\r\n+ipcMain.handle('get-plugins', async () => {\r\n+  try {\r\n+    const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n+    if (!fs.existsSync(pluginsDir)) return [];\r\n \r\n-  const pluginFolders = fs.readdirSync(pluginsDir);\r\n-  return pluginFolders.map(folder => {\r\n-    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n-    if (fs.existsSync(manifestPath)) {\r\n-      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n-      return {\r\n-        name: manifest.name,\r\n-        description: manifest.description,\r\n-      };\r\n+    const pluginFolders = fs.readdirSync(pluginsDir);\r\n+    const plugins = [];\r\n+\r\n+    for (const folder of pluginFolders) {\r\n+      const pluginPath = path.join(pluginsDir, folder);\r\n+\r\n+      // 跳过非目录文件\r\n+      if (!fs.statSync(pluginPath).isDirectory()) {\r\n+        continue;\r\n+      }\r\n+\r\n+      const manifestPath = path.join(pluginPath, 'manifest.json');\r\n+\r\n+      try {\r\n+        if (fs.existsSync(manifestPath)) {\r\n+          const manifestContent = fs.readFileSync(manifestPath, 'utf-8');\r\n+          const manifest = JSON.parse(manifestContent);\r\n+\r\n+          const loadStatus = pluginLoadStatus.get(manifest.name || folder);\r\n+\r\n+          plugins.push({\r\n+            name: manifest.name || folder,\r\n+            description: manifest.description || 'No description',\r\n+            version: manifest.version || '1.0.0',\r\n+            status: loadStatus?.status || 'unknown',\r\n+            error: loadStatus?.error\r\n+          });\r\n+        }\r\n+      } catch (error) {\r\n+        // 如果单个插件解析失败，记录错误但继续处理其他插件\r\n+        console.error(`Error parsing plugin ${folder}:`, error);\r\n+        plugins.push({\r\n+          name: folder,\r\n+          description: 'Error loading plugin',\r\n+          version: 'unknown',\r\n+          status: 'failed',\r\n+          error: error instanceof Error ? error.message : 'Unknown error'\r\n+        });\r\n+      }\r\n     }\r\n-    return null;\r\n-  }).filter(Boolean);\r\n+\r\n+    return plugins;\r\n+  } catch (error) {\r\n+    console.error('Error getting plugins list:', error);\r\n+    throw new Error('Failed to get plugins list');\r\n+  }\r\n });\r\n \r\n // 添加插件安装功能\r\n ipcMain.handle('install-plugin', (_, pluginPath) => {\r\n"}, {"date": 1750234750010, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -199,24 +199,56 @@\n   }\r\n });\r\n \r\n // 添加插件安装功能\r\n-ipcMain.handle('install-plugin', (_, pluginPath) => {\r\n-  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  if (!fs.existsSync(pluginsDir)) {\r\n-    fs.mkdirSync(pluginsDir);\r\n-  }\r\n+ipcMain.handle('install-plugin', async (_, pluginPath: string) => {\r\n+  try {\r\n+    if (!pluginPath || typeof pluginPath !== 'string') {\r\n+      throw new Error('Invalid plugin path provided');\r\n+    }\r\n \r\n-  const pluginName = path.basename(pluginPath, path.extname(pluginPath));\r\n-  const targetDir = path.join(pluginsDir, pluginName);\r\n+    if (!fs.existsSync(pluginPath)) {\r\n+      throw new Error('Plugin file does not exist');\r\n+    }\r\n \r\n-  if (fs.existsSync(targetDir)) {\r\n-    throw new Error('Plugin already exists');\r\n+    const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n+    if (!fs.existsSync(pluginsDir)) {\r\n+      fs.mkdirSync(pluginsDir, { recursive: true });\r\n+    }\r\n+\r\n+    const pluginName = path.basename(pluginPath, path.extname(pluginPath));\r\n+    const targetDir = path.join(pluginsDir, pluginName);\r\n+\r\n+    if (fs.existsSync(targetDir)) {\r\n+      throw new Error(`Plugin \"${pluginName}\" already exists`);\r\n+    }\r\n+\r\n+    // 创建插件目录\r\n+    fs.mkdirSync(targetDir, { recursive: true });\r\n+\r\n+    try {\r\n+      // 复制插件文件\r\n+      fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));\r\n+\r\n+      // 尝试重新加载插件\r\n+      await loadPlugins();\r\n+\r\n+      return {\r\n+        success: true,\r\n+        message: `Plugin \"${pluginName}\" installed successfully`\r\n+      };\r\n+    } catch (copyError) {\r\n+      // 如果复制失败，清理已创建的目录\r\n+      if (fs.existsSync(targetDir)) {\r\n+        fs.rmSync(targetDir, { recursive: true, force: true });\r\n+      }\r\n+      throw copyError;\r\n+    }\r\n+  } catch (error) {\r\n+    const errorMessage = error instanceof Error ? error.message : 'Unknown installation error';\r\n+    console.error('Plugin installation failed:', errorMessage);\r\n+    throw new Error(`Installation failed: ${errorMessage}`);\r\n   }\r\n-\r\n-  fs.mkdirSync(targetDir);\r\n-  fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));\r\n-  return true;\r\n });\r\n \r\n // 添加插件卸载功能\r\n ipcMain.handle('uninstall-plugin', (_, pluginName) => {\r\n"}, {"date": 1750234772070, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -250,18 +250,61 @@\n   }\r\n });\r\n \r\n // 添加插件卸载功能\r\n-ipcMain.handle('uninstall-plugin', (_, pluginName) => {\r\n-  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n-  const targetDir = path.join(pluginsDir, pluginName);\r\n+ipcMain.handle('uninstall-plugin', async (_, pluginName: string) => {\r\n+  try {\r\n+    if (!pluginName || typeof pluginName !== 'string') {\r\n+      throw new Error('Invalid plugin name provided');\r\n+    }\r\n \r\n-  if (fs.existsSync(targetDir)) {\r\n+    const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n+    const targetDir = path.join(pluginsDir, pluginName);\r\n+\r\n+    if (!fs.existsSync(targetDir)) {\r\n+      throw new Error(`Plugin \"${pluginName}\" not found`);\r\n+    }\r\n+\r\n+    // 检查是否为目录\r\n+    if (!fs.statSync(targetDir).isDirectory()) {\r\n+      throw new Error(`\"${pluginName}\" is not a valid plugin directory`);\r\n+    }\r\n+\r\n+    // 尝试调用插件的deactivate函数（如果存在）\r\n+    try {\r\n+      if (pluginFeatures[pluginName]) {\r\n+        const manifestPath = path.join(targetDir, 'manifest.json');\r\n+        if (fs.existsSync(manifestPath)) {\r\n+          const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n+          const pluginEntry = path.join(targetDir, manifest.main);\r\n+          if (fs.existsSync(pluginEntry)) {\r\n+            const plugin = await import(pluginEntry);\r\n+            if (typeof plugin.deactivate === 'function') {\r\n+              await plugin.deactivate();\r\n+            }\r\n+          }\r\n+        }\r\n+        // 从内存中移除插件\r\n+        delete pluginFeatures[pluginName];\r\n+        pluginLoadStatus.delete(pluginName);\r\n+      }\r\n+    } catch (deactivateError) {\r\n+      console.warn(`Warning: Failed to properly deactivate plugin \"${pluginName}\":`, deactivateError);\r\n+      // 继续卸载，即使deactivate失败\r\n+    }\r\n+\r\n+    // 删除插件目录\r\n     fs.rmSync(targetDir, { recursive: true, force: true });\r\n-    return true;\r\n+\r\n+    return {\r\n+      success: true,\r\n+      message: `Plugin \"${pluginName}\" uninstalled successfully`\r\n+    };\r\n+  } catch (error) {\r\n+    const errorMessage = error instanceof Error ? error.message : 'Unknown uninstallation error';\r\n+    console.error('Plugin uninstallation failed:', errorMessage);\r\n+    throw new Error(`Uninstallation failed: ${errorMessage}`);\r\n   }\r\n-\r\n-  throw new Error('Plugin not found');\r\n });\r\n \r\n ipcMain.handle('get-plugin-feature', (_, pluginName, featureName) => {\r\n   if (pluginFeatures[pluginName] && pluginFeatures[pluginName][featureName]) {\r\n"}, {"date": 1750234790901, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -305,15 +305,43 @@\n     throw new Error(`Uninstallation failed: ${errorMessage}`);\r\n   }\r\n });\r\n \r\n-ipcMain.handle('get-plugin-feature', (_, pluginName, featureName) => {\r\n-  if (pluginFeatures[pluginName] && pluginFeatures[pluginName][featureName]) {\r\n-    return pluginFeatures[pluginName][featureName];\r\n+ipcMain.handle('get-plugin-feature', async (_, pluginName: string, featureName: string) => {\r\n+  try {\r\n+    if (!pluginName || !featureName) {\r\n+      throw new Error('Plugin name and feature name are required');\r\n+    }\r\n+\r\n+    if (!pluginFeatures[pluginName]) {\r\n+      throw new Error(`Plugin \"${pluginName}\" not found or not loaded`);\r\n+    }\r\n+\r\n+    if (!pluginFeatures[pluginName][featureName]) {\r\n+      const availableFeatures = Object.keys(pluginFeatures[pluginName]);\r\n+      throw new Error(`Feature \"${featureName}\" not found in plugin \"${pluginName}\". Available features: ${availableFeatures.join(', ')}`);\r\n+    }\r\n+\r\n+    const feature = pluginFeatures[pluginName][featureName];\r\n+\r\n+    if (typeof feature !== 'function') {\r\n+      throw new Error(`Feature \"${featureName}\" in plugin \"${pluginName}\" is not a function`);\r\n+    }\r\n+\r\n+    return feature;\r\n+  } catch (error) {\r\n+    const errorMessage = error instanceof Error ? error.message : 'Unknown error accessing plugin feature';\r\n+    console.error('Plugin feature access failed:', errorMessage);\r\n+    throw new Error(errorMessage);\r\n   }\r\n-  throw new Error(`Feature ${featureName} not found in plugin ${pluginName}`);\r\n });\r\n \r\n+// 添加获取插件加载状态的处理程序\r\n+ipcMain.handle('get-plugin-status', () => {\r\n+  const statusArray = Array.from(pluginLoadStatus.values());\r\n+  return statusArray;\r\n+});\r\n+\r\n // Quit when all windows are closed, except on macOS. There, it's common\r\n // for applications and their menu bar to stay active until the user quits\r\n // explicitly with Cmd + Q.\r\n app.on('window-all-closed', () => {\r\n"}, {"date": 1750234803921, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -358,8 +358,8 @@\n     createWindow()\r\n   }\r\n })\r\n \r\n-app.whenReady().then(() => {\r\n+app.whenReady().then(async () => {\r\n   createWindow()\r\n-  loadPlugins();\r\n+  await loadPlugins();\r\n })\r\n"}, {"date": 1750235054032, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,16 @@\n import { app, BrowserWindow, ipcMain } from 'electron'\r\n import { fileURLToPath } from 'node:url'\r\n import path from 'node:path'\r\n import fs from 'fs'\r\n+import type {\r\n+  PluginManifest,\r\n+  PluginInfo,\r\n+  PluginFeatures,\r\n+  PluginLoadStatus,\r\n+  PluginOperationResult,\r\n+  PluginModule\r\n+} from '../src/types/plugin'\r\n \r\n const __dirname = path.dirname(fileURLToPath(import.meta.url))\r\n \r\n // The built directory structure\r\n"}, {"date": 1750235067982, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,24 +53,12 @@\n     win.loadFile(path.join(RENDERER_DIST, 'index.html'))\r\n   }\r\n }\r\n \r\n-// 为 pluginFeatures 定义类型\r\n-interface PluginFeatures {\r\n-  [pluginName: string]: {\r\n-    [featureName: string]: (...args: any[]) => any;\r\n-  };\r\n-}\r\n+// 插件功能存储：插件名 -> 功能集合\r\n+const pluginFeatures: Record<string, PluginFeatures> = {};\r\n \r\n-const pluginFeatures: PluginFeatures = {};\r\n-\r\n // 插件加载状态跟踪\r\n-interface PluginLoadStatus {\r\n-  name: string;\r\n-  status: 'loading' | 'loaded' | 'failed';\r\n-  error?: string;\r\n-}\r\n-\r\n const pluginLoadStatus: Map<string, PluginLoadStatus> = new Map();\r\n \r\n async function loadPlugins() {\r\n   const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n"}, {"date": 1750235080927, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -85,12 +85,12 @@\n           throw new Error(`Manifest file not found: ${manifestPath}`);\r\n         }\r\n \r\n         const manifestContent = fs.readFileSync(manifestPath, 'utf-8');\r\n-        let manifest;\r\n+        let manifest: PluginManifest;\r\n \r\n         try {\r\n-          manifest = JSON.parse(manifestContent);\r\n+          manifest = JSON.parse(manifestContent) as PluginManifest;\r\n         } catch (parseError) {\r\n           const errorMessage = parseError instanceof Error ? parseError.message : 'Invalid JSON format';\r\n           throw new Error(`Invalid JSON in manifest: ${errorMessage}`);\r\n         }\r\n"}, {"date": 1750235095088, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -106,18 +106,22 @@\n         if (!fs.existsSync(pluginEntry)) {\r\n           throw new Error(`Plugin entry file not found: ${pluginEntry}`);\r\n         }\r\n \r\n-        const plugin = await import(pluginEntry);\r\n+        const plugin = await import(pluginEntry) as PluginModule;\r\n \r\n         if (typeof plugin.activate !== 'function') {\r\n           throw new Error('Plugin must export an \"activate\" function');\r\n         }\r\n \r\n-        const features = plugin.activate();\r\n+        const features = await plugin.activate();\r\n         if (features && typeof features === 'object') {\r\n           pluginFeatures[manifest.name] = features;\r\n-          pluginLoadStatus.set(manifest.name, { name: manifest.name, status: 'loaded' });\r\n+          pluginLoadStatus.set(manifest.name, {\r\n+            name: manifest.name,\r\n+            status: 'loaded',\r\n+            loadedAt: Date.now()\r\n+          });\r\n           console.log(`Plugin \"${manifest.name}\" loaded successfully`);\r\n         } else {\r\n           throw new Error('Plugin activate function must return an object with features');\r\n         }\r\n"}, {"date": 1750235116386, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -145,15 +145,15 @@\n   }\r\n }\r\n \r\n // 添加 IPC 处理程序以获取插件列表\r\n-ipcMain.handle('get-plugins', async () => {\r\n+ipcMain.handle('get-plugins', async (): Promise<PluginInfo[]> => {\r\n   try {\r\n     const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n     if (!fs.existsSync(pluginsDir)) return [];\r\n \r\n     const pluginFolders = fs.readdirSync(pluginsDir);\r\n-    const plugins = [];\r\n+    const plugins: PluginInfo[] = [];\r\n \r\n     for (const folder of pluginFolders) {\r\n       const pluginPath = path.join(pluginsDir, folder);\r\n \r\n"}, {"date": 1750235129744, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -166,18 +166,20 @@\n \r\n       try {\r\n         if (fs.existsSync(manifestPath)) {\r\n           const manifestContent = fs.readFileSync(manifestPath, 'utf-8');\r\n-          const manifest = JSON.parse(manifestContent);\r\n+          const manifest = JSON.parse(manifestContent) as PluginManifest;\r\n \r\n           const loadStatus = pluginLoadStatus.get(manifest.name || folder);\r\n \r\n           plugins.push({\r\n             name: manifest.name || folder,\r\n             description: manifest.description || 'No description',\r\n             version: manifest.version || '1.0.0',\r\n             status: loadStatus?.status || 'unknown',\r\n-            error: loadStatus?.error\r\n+            error: loadStatus?.error,\r\n+            author: manifest.author,\r\n+            license: manifest.license\r\n           });\r\n         }\r\n       } catch (error) {\r\n         // 如果单个插件解析失败，记录错误但继续处理其他插件\r\n"}, {"date": 1750235143381, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -201,9 +201,9 @@\n   }\r\n });\r\n \r\n // 添加插件安装功能\r\n-ipcMain.handle('install-plugin', async (_, pluginPath: string) => {\r\n+ipcMain.handle('install-plugin', async (_, pluginPath: string): Promise<PluginOperationResult> => {\r\n   try {\r\n     if (!pluginPath || typeof pluginPath !== 'string') {\r\n       throw new Error('Invalid plugin path provided');\r\n     }\r\n"}, {"date": 1750235154211, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -252,9 +252,9 @@\n   }\r\n });\r\n \r\n // 添加插件卸载功能\r\n-ipcMain.handle('uninstall-plugin', async (_, pluginName: string) => {\r\n+ipcMain.handle('uninstall-plugin', async (_, pluginName: string): Promise<PluginOperationResult> => {\r\n   try {\r\n     if (!pluginName || typeof pluginName !== 'string') {\r\n       throw new Error('Invalid plugin name provided');\r\n     }\r\n"}, {"date": 1750235170277, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -275,12 +275,12 @@\n     try {\r\n       if (pluginFeatures[pluginName]) {\r\n         const manifestPath = path.join(targetDir, 'manifest.json');\r\n         if (fs.existsSync(manifestPath)) {\r\n-          const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n+          const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8')) as PluginManifest;\r\n           const pluginEntry = path.join(targetDir, manifest.main);\r\n           if (fs.existsSync(pluginEntry)) {\r\n-            const plugin = await import(pluginEntry);\r\n+            const plugin = await import(pluginEntry) as PluginModule;\r\n             if (typeof plugin.deactivate === 'function') {\r\n               await plugin.deactivate();\r\n             }\r\n           }\r\n"}, {"date": 1750235241438, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -59,8 +59,14 @@\n \r\n // 插件加载状态跟踪\r\n const pluginLoadStatus: Map<string, PluginLoadStatus> = new Map();\r\n \r\n+// 文件监听器存储\r\n+const pluginWatchers: Map<string, fs.FSWatcher> = new Map();\r\n+\r\n+// 热重载开关\r\n+let hotReloadEnabled = process.env.NODE_ENV === 'development';\r\n+\r\n async function loadPlugins() {\r\n   const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n   if (!fs.existsSync(pluginsDir)) {\r\n     console.log('Plugins directory not found, creating...');\r\n"}, {"date": 1750235275959, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -65,8 +65,153 @@\n \r\n // 热重载开关\r\n let hotReloadEnabled = process.env.NODE_ENV === 'development';\r\n \r\n+/**\r\n+ * 重新加载单个插件\r\n+ */\r\n+async function reloadPlugin(pluginName: string, pluginPath: string) {\r\n+  try {\r\n+    console.log(`Hot reloading plugin: ${pluginName}`);\r\n+\r\n+    // 停用现有插件\r\n+    if (pluginFeatures[pluginName]) {\r\n+      try {\r\n+        const manifestPath = path.join(pluginPath, 'manifest.json');\r\n+        if (fs.existsSync(manifestPath)) {\r\n+          const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8')) as PluginManifest;\r\n+          const pluginEntry = path.join(pluginPath, manifest.main);\r\n+\r\n+          // 清除模块缓存\r\n+          delete require.cache[require.resolve(pluginEntry)];\r\n+\r\n+          // 尝试调用deactivate\r\n+          const oldPlugin = await import(pluginEntry) as PluginModule;\r\n+          if (typeof oldPlugin.deactivate === 'function') {\r\n+            await oldPlugin.deactivate();\r\n+          }\r\n+        }\r\n+      } catch (deactivateError) {\r\n+        console.warn(`Warning: Failed to deactivate plugin \"${pluginName}\":`, deactivateError);\r\n+      }\r\n+\r\n+      // 从内存中移除\r\n+      delete pluginFeatures[pluginName];\r\n+    }\r\n+\r\n+    // 重新加载插件\r\n+    await loadSinglePlugin(pluginName, pluginPath);\r\n+\r\n+    // 通知渲染进程插件已重新加载\r\n+    if (win && !win.isDestroyed()) {\r\n+      win.webContents.send('plugin-reloaded', { pluginName, status: 'success' });\r\n+    }\r\n+\r\n+  } catch (error) {\r\n+    console.error(`Failed to reload plugin \"${pluginName}\":`, error);\r\n+    pluginLoadStatus.set(pluginName, {\r\n+      name: pluginName,\r\n+      status: 'failed',\r\n+      error: error instanceof Error ? error.message : 'Hot reload failed'\r\n+    });\r\n+\r\n+    // 通知渲染进程重载失败\r\n+    if (win && !win.isDestroyed()) {\r\n+      win.webContents.send('plugin-reloaded', {\r\n+        pluginName,\r\n+        status: 'error',\r\n+        error: error instanceof Error ? error.message : 'Hot reload failed'\r\n+      });\r\n+    }\r\n+  }\r\n+}\r\n+\r\n+/**\r\n+ * 加载单个插件\r\n+ */\r\n+async function loadSinglePlugin(pluginName: string, pluginPath: string) {\r\n+  const manifestPath = path.join(pluginPath, 'manifest.json');\r\n+\r\n+  if (!fs.existsSync(manifestPath)) {\r\n+    throw new Error(`Manifest file not found: ${manifestPath}`);\r\n+  }\r\n+\r\n+  const manifestContent = fs.readFileSync(manifestPath, 'utf-8');\r\n+  let manifest: PluginManifest;\r\n+\r\n+  try {\r\n+    manifest = JSON.parse(manifestContent) as PluginManifest;\r\n+  } catch (parseError) {\r\n+    const errorMessage = parseError instanceof Error ? parseError.message : 'Invalid JSON format';\r\n+    throw new Error(`Invalid JSON in manifest: ${errorMessage}`);\r\n+  }\r\n+\r\n+  // 验证必需字段\r\n+  if (!manifest.name || !manifest.main) {\r\n+    throw new Error('Manifest must contain \"name\" and \"main\" fields');\r\n+  }\r\n+\r\n+  pluginLoadStatus.set(manifest.name, { name: manifest.name, status: 'loading' });\r\n+\r\n+  const pluginEntry = path.join(pluginPath, manifest.main);\r\n+  if (!fs.existsSync(pluginEntry)) {\r\n+    throw new Error(`Plugin entry file not found: ${pluginEntry}`);\r\n+  }\r\n+\r\n+  const plugin = await import(pluginEntry) as PluginModule;\r\n+\r\n+  if (typeof plugin.activate !== 'function') {\r\n+    throw new Error('Plugin must export an \"activate\" function');\r\n+  }\r\n+\r\n+  const features = await plugin.activate();\r\n+  if (features && typeof features === 'object') {\r\n+    pluginFeatures[manifest.name] = features;\r\n+    pluginLoadStatus.set(manifest.name, {\r\n+      name: manifest.name,\r\n+      status: 'loaded',\r\n+      loadedAt: Date.now()\r\n+    });\r\n+\r\n+    // 设置热重载监听器\r\n+    if (hotReloadEnabled) {\r\n+      setupHotReload(manifest.name, pluginPath);\r\n+    }\r\n+\r\n+    console.log(`Plugin \"${manifest.name}\" loaded successfully`);\r\n+  } else {\r\n+    throw new Error('Plugin activate function must return an object with features');\r\n+  }\r\n+}\r\n+\r\n+/**\r\n+ * 设置插件热重载监听\r\n+ */\r\n+function setupHotReload(pluginName: string, pluginPath: string) {\r\n+  // 清除现有监听器\r\n+  if (pluginWatchers.has(pluginName)) {\r\n+    pluginWatchers.get(pluginName)?.close();\r\n+  }\r\n+\r\n+  try {\r\n+    const watcher = fs.watch(pluginPath, { recursive: true }, (eventType, filename) => {\r\n+      if (filename && (filename.endsWith('.js') || filename.endsWith('.json'))) {\r\n+        console.log(`Plugin file changed: ${filename} in ${pluginName}`);\r\n+\r\n+        // 防抖：延迟重载以避免频繁触发\r\n+        setTimeout(() => {\r\n+          reloadPlugin(pluginName, pluginPath);\r\n+        }, 500);\r\n+      }\r\n+    });\r\n+\r\n+    pluginWatchers.set(pluginName, watcher);\r\n+    console.log(`Hot reload enabled for plugin: ${pluginName}`);\r\n+  } catch (error) {\r\n+    console.warn(`Failed to setup hot reload for plugin \"${pluginName}\":`, error);\r\n+  }\r\n+}\r\n+\r\n async function loadPlugins() {\r\n   const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n   if (!fs.existsSync(pluginsDir)) {\r\n     console.log('Plugins directory not found, creating...');\r\n"}, {"date": 1750235286270, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -192,9 +192,9 @@\n     pluginWatchers.get(pluginName)?.close();\r\n   }\r\n \r\n   try {\r\n-    const watcher = fs.watch(pluginPath, { recursive: true }, (eventType, filename) => {\r\n+    const watcher = fs.watch(pluginPath, { recursive: true }, (_, filename) => {\r\n       if (filename && (filename.endsWith('.js') || filename.endsWith('.json'))) {\r\n         console.log(`Plugin file changed: ${filename} in ${pluginName}`);\r\n \r\n         // 防抖：延迟重载以避免频繁触发\r\n"}, {"date": 1750235308152, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -231,53 +231,9 @@\n \r\n       const manifestPath = path.join(pluginPath, 'manifest.json');\r\n \r\n       try {\r\n-        if (!fs.existsSync(manifestPath)) {\r\n-          throw new Error(`Manifest file not found: ${manifestPath}`);\r\n-        }\r\n-\r\n-        const manifestContent = fs.readFileSync(manifestPath, 'utf-8');\r\n-        let manifest: PluginManifest;\r\n-\r\n-        try {\r\n-          manifest = JSON.parse(manifestContent) as PluginManifest;\r\n-        } catch (parseError) {\r\n-          const errorMessage = parseError instanceof Error ? parseError.message : 'Invalid JSON format';\r\n-          throw new Error(`Invalid JSON in manifest: ${errorMessage}`);\r\n-        }\r\n-\r\n-        // 验证必需字段\r\n-        if (!manifest.name || !manifest.main) {\r\n-          throw new Error('Manifest must contain \"name\" and \"main\" fields');\r\n-        }\r\n-\r\n-        pluginLoadStatus.set(manifest.name, { name: manifest.name, status: 'loading' });\r\n-\r\n-        const pluginEntry = path.join(pluginPath, manifest.main);\r\n-        if (!fs.existsSync(pluginEntry)) {\r\n-          throw new Error(`Plugin entry file not found: ${pluginEntry}`);\r\n-        }\r\n-\r\n-        const plugin = await import(pluginEntry) as PluginModule;\r\n-\r\n-        if (typeof plugin.activate !== 'function') {\r\n-          throw new Error('Plugin must export an \"activate\" function');\r\n-        }\r\n-\r\n-        const features = await plugin.activate();\r\n-        if (features && typeof features === 'object') {\r\n-          pluginFeatures[manifest.name] = features;\r\n-          pluginLoadStatus.set(manifest.name, {\r\n-            name: manifest.name,\r\n-            status: 'loaded',\r\n-            loadedAt: Date.now()\r\n-          });\r\n-          console.log(`Plugin \"${manifest.name}\" loaded successfully`);\r\n-        } else {\r\n-          throw new Error('Plugin activate function must return an object with features');\r\n-        }\r\n-\r\n+        await loadSinglePlugin(folder, pluginPath);\r\n       } catch (error) {\r\n         const errorMessage = error instanceof Error ? error.message : 'Unknown error';\r\n         console.error(`Failed to load plugin \"${folder}\":`, errorMessage);\r\n         pluginLoadStatus.set(folder, {\r\n"}], "date": 1750233236480, "name": "Commit-0", "content": "import { app, BrowserWindow, ipcMain } from 'electron'\r\nimport { fileURLToPath } from 'node:url'\r\nimport path from 'node:path'\r\nimport fs from 'fs'\r\n\r\nconst __dirname = path.dirname(fileURLToPath(import.meta.url))\r\n\r\n// The built directory structure\r\n//\r\n// ├─┬─┬ dist\r\n// │ │ └── index.html\r\n// │ │\r\n// │ ├─┬ dist-electron\r\n// │ │ ├── main.js\r\n// │ │ └── preload.mjs\r\n// │\r\nprocess.env.APP_ROOT = path.join(__dirname, '..')\r\n\r\n// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x\r\nexport const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']\r\nexport const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')\r\nexport const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')\r\n\r\nprocess.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST\r\n\r\nlet win: BrowserWindow | null\r\n\r\nfunction createWindow() {\r\n  win = new BrowserWindow({\r\n    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),\r\n    webPreferences: {\r\n      preload: path.join(__dirname, 'preload.mjs'),\r\n    },\r\n  })\r\n\r\n  // Test active push message to Renderer-process.\r\n  win.webContents.on('did-finish-load', () => {\r\n    win?.webContents.send('main-process-message', (new Date).toLocaleString())\r\n  })\r\n\r\n  if (VITE_DEV_SERVER_URL) {\r\n    win.loadURL(VITE_DEV_SERVER_URL)\r\n  } else {\r\n    // win.loadFile('dist/index.html')\r\n    win.loadFile(path.join(RENDERER_DIST, 'index.html'))\r\n  }\r\n}\r\n\r\nconst pluginFeatures = {};\r\n\r\nfunction loadPlugins() {\r\n  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n  if (!fs.existsSync(pluginsDir)) return;\r\n\r\n  const pluginFolders = fs.readdirSync(pluginsDir);\r\n  pluginFolders.forEach(folder => {\r\n    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n    if (fs.existsSync(manifestPath)) {\r\n      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n      const pluginEntry = path.join(pluginsDir, folder, manifest.main);\r\n      if (fs.existsSync(pluginEntry)) {\r\n        import(pluginEntry).then(plugin => {\r\n          if (plugin.activate) {\r\n            const features = plugin.activate();\r\n            if (features) {\r\n              pluginFeatures[manifest.name] = features;\r\n            }\r\n          }\r\n        }).catch(err => console.error(`Failed to load plugin ${folder}:`, err));\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\n// 添加 IPC 处理程序以获取插件列表\r\nipcMain.handle('get-plugins', () => {\r\n  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n  if (!fs.existsSync(pluginsDir)) return [];\r\n\r\n  const pluginFolders = fs.readdirSync(pluginsDir);\r\n  return pluginFolders.map(folder => {\r\n    const manifestPath = path.join(pluginsDir, folder, 'manifest.json');\r\n    if (fs.existsSync(manifestPath)) {\r\n      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));\r\n      return {\r\n        name: manifest.name,\r\n        description: manifest.description,\r\n      };\r\n    }\r\n    return null;\r\n  }).filter(Boolean);\r\n});\r\n\r\n// 添加插件安装功能\r\nipcMain.handle('install-plugin', (_, pluginPath) => {\r\n  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n  if (!fs.existsSync(pluginsDir)) {\r\n    fs.mkdirSync(pluginsDir);\r\n  }\r\n\r\n  const pluginName = path.basename(pluginPath, path.extname(pluginPath));\r\n  const targetDir = path.join(pluginsDir, pluginName);\r\n\r\n  if (fs.existsSync(targetDir)) {\r\n    throw new Error('Plugin already exists');\r\n  }\r\n\r\n  fs.mkdirSync(targetDir);\r\n  fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));\r\n  return true;\r\n});\r\n\r\n// 添加插件卸载功能\r\nipcMain.handle('uninstall-plugin', (_, pluginName) => {\r\n  const pluginsDir = path.join(process.env.APP_ROOT, 'plugins');\r\n  const targetDir = path.join(pluginsDir, pluginName);\r\n\r\n  if (fs.existsSync(targetDir)) {\r\n    fs.rmSync(targetDir, { recursive: true, force: true });\r\n    return true;\r\n  }\r\n\r\n  throw new Error('Plugin not found');\r\n});\r\n\r\n// Quit when all windows are closed, except on macOS. There, it's common\r\n// for applications and their menu bar to stay active until the user quits\r\n// explicitly with Cmd + Q.\r\napp.on('window-all-closed', () => {\r\n  if (process.platform !== 'darwin') {\r\n    app.quit()\r\n    win = null\r\n  }\r\n})\r\n\r\napp.on('activate', () => {\r\n  // On OS X it's common to re-create a window in the app when the\r\n  // dock icon is clicked and there are no other windows open.\r\n  if (BrowserWindow.getAllWindows().length === 0) {\r\n    createWindow()\r\n  }\r\n})\r\n\r\napp.whenReady().then(() => {\r\n  createWindow()\r\n  loadPlugins();\r\n})\r\n"}]}