{"sourceFile": "src/types/plugin.ts", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1750235490642, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750235814106, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -68,13 +68,29 @@\n  */\n export type PluginFeatures = Record<string, (...args: any[]) => any>;\n \n /**\n+ * 插件API接口（简化版，用于类型定义）\n+ */\n+export interface PluginAPI {\n+  system: any;\n+  fs: any;\n+  dialog: any;\n+  notification: any;\n+  shell: any;\n+  exec: any;\n+  window: any;\n+  logger: any;\n+  storage: any;\n+  events: any;\n+}\n+\n+/**\n  * 插件模块接口\n  */\n export interface PluginModule {\n   /** 插件激活函数 */\n-  activate: () => PluginFeatures | Promise<PluginFeatures>;\n+  activate: (api?: PluginAPI) => PluginFeatures | Promise<PluginFeatures>;\n   /** 插件停用函数（可选） */\n   deactivate?: () => void | Promise<void>;\n }\n \n"}], "date": 1750235490642, "name": "Commit-0", "content": "// 插件系统类型定义\n\n/**\n * 插件状态枚举\n */\nexport type PluginStatus = 'loading' | 'loaded' | 'failed' | 'unknown';\n\n/**\n * 插件清单文件接口\n */\nexport interface PluginManifest {\n  /** 插件名称 */\n  name: string;\n  /** 插件描述 */\n  description: string;\n  /** 插件版本 */\n  version?: string;\n  /** 插件入口文件 */\n  main: string;\n  /** 插件作者 */\n  author?: string;\n  /** 插件许可证 */\n  license?: string;\n  /** 插件关键词 */\n  keywords?: string[];\n  /** 插件依赖 */\n  dependencies?: Record<string, string>;\n  /** 插件权限要求 */\n  permissions?: string[];\n  /** 最小系统版本要求 */\n  minSystemVersion?: string;\n}\n\n/**\n * 插件信息接口（用于UI显示）\n */\nexport interface PluginInfo {\n  /** 插件名称 */\n  name: string;\n  /** 插件描述 */\n  description: string;\n  /** 插件版本 */\n  version?: string;\n  /** 插件状态 */\n  status: PluginStatus;\n  /** 错误信息（如果加载失败） */\n  error?: string;\n  /** 插件作者 */\n  author?: string;\n  /** 插件许可证 */\n  license?: string;\n}\n\n/**\n * 插件功能接口\n */\nexport interface PluginFeature {\n  /** 功能名称 */\n  name: string;\n  /** 功能描述 */\n  description?: string;\n  /** 功能函数 */\n  handler: (...args: any[]) => any;\n}\n\n/**\n * 插件功能集合\n */\nexport type PluginFeatures = Record<string, (...args: any[]) => any>;\n\n/**\n * 插件模块接口\n */\nexport interface PluginModule {\n  /** 插件激活函数 */\n  activate: () => PluginFeatures | Promise<PluginFeatures>;\n  /** 插件停用函数（可选） */\n  deactivate?: () => void | Promise<void>;\n}\n\n/**\n * 插件加载状态\n */\nexport interface PluginLoadStatus {\n  /** 插件名称 */\n  name: string;\n  /** 加载状态 */\n  status: PluginStatus;\n  /** 错误信息 */\n  error?: string;\n  /** 加载时间戳 */\n  loadedAt?: number;\n}\n\n/**\n * 插件操作结果\n */\nexport interface PluginOperationResult {\n  /** 操作是否成功 */\n  success: boolean;\n  /** 操作消息 */\n  message: string;\n  /** 错误详情（如果失败） */\n  error?: string;\n}\n\n/**\n * IPC 通信接口\n */\nexport interface PluginIPC {\n  /** 获取插件列表 */\n  'get-plugins': () => Promise<PluginInfo[]>;\n  /** 安装插件 */\n  'install-plugin': (pluginPath: string) => Promise<PluginOperationResult>;\n  /** 卸载插件 */\n  'uninstall-plugin': (pluginName: string) => Promise<PluginOperationResult>;\n  /** 获取插件功能 */\n  'get-plugin-feature': (pluginName: string, featureName: string) => Promise<(...args: any[]) => any>;\n  /** 获取插件状态 */\n  'get-plugin-status': () => Promise<PluginLoadStatus[]>;\n}\n\n// Window接口扩展在electron-env.d.ts中已定义\n\n/**\n * 插件系统配置\n */\nexport interface PluginSystemConfig {\n  /** 插件目录路径 */\n  pluginsDir: string;\n  /** 是否启用沙箱模式 */\n  sandboxMode: boolean;\n  /** 最大插件数量 */\n  maxPlugins?: number;\n  /** 插件加载超时时间（毫秒） */\n  loadTimeout?: number;\n}\n\n/**\n * 插件安全策略\n */\nexport interface PluginSecurityPolicy {\n  /** 允许的文件系统访问路径 */\n  allowedPaths?: string[];\n  /** 允许的网络访问域名 */\n  allowedDomains?: string[];\n  /** 禁用的Node.js模块 */\n  disallowedModules?: string[];\n  /** 是否允许执行系统命令 */\n  allowSystemCommands?: boolean;\n}\n"}]}