<template>
  <div class="security-settings">
    <h2>安全设置</h2>
    
    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <strong>错误:</strong> {{ error }}
      <button @click="clearError" class="close-btn">×</button>
    </div>
    
    <!-- 成功提示 -->
    <div v-if="successMessage" class="success-message">
      <strong>成功:</strong> {{ successMessage }}
      <button @click="clearSuccess" class="close-btn">×</button>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-message">
      <span class="spinner"></span> {{ loadingMessage }}
    </div>
    
    <!-- 安全策略设置 -->
    <div class="policy-section">
      <h3>安全策略</h3>
      <div class="policy-grid">
        <div class="policy-item">
          <label>
            <input 
              type="checkbox" 
              v-model="policy.enablePermissionCheck"
              @change="updatePolicy"
            />
            启用权限检查
          </label>
          <p class="policy-description">检查插件是否有权限访问特定功能</p>
        </div>
        
        <div class="policy-item">
          <label>
            <input 
              type="checkbox" 
              v-model="policy.enableCodeSigning"
              @change="updatePolicy"
            />
            启用代码签名验证
          </label>
          <p class="policy-description">验证插件的数字签名（推荐在生产环境中启用）</p>
        </div>
        
        <div class="policy-item">
          <label>
            <input 
              type="checkbox" 
              v-model="policy.enableSandbox"
              @change="updatePolicy"
            />
            启用沙箱模式
          </label>
          <p class="policy-description">在隔离环境中运行插件（推荐在生产环境中启用）</p>
        </div>
        
        <div class="policy-item">
          <label>
            最大文件大小 (MB):
            <input 
              type="number" 
              v-model.number="maxFileSizeMB"
              @change="updateMaxFileSize"
              min="1"
              max="100"
            />
          </label>
          <p class="policy-description">限制插件文件的最大大小</p>
        </div>
      </div>
    </div>
    
    <!-- 信任的插件列表 -->
    <div class="trusted-plugins-section">
      <h3>信任的插件</h3>
      <p class="section-description">信任的插件可以访问所有功能，无需权限检查</p>
      
      <div class="add-trusted-plugin">
        <input 
          type="text" 
          v-model="newTrustedPlugin"
          placeholder="输入插件名称"
          @keyup.enter="addTrustedPlugin"
        />
        <button @click="addTrustedPlugin" :disabled="!newTrustedPlugin.trim()">
          添加信任插件
        </button>
      </div>
      
      <div v-if="trustedPlugins.length === 0" class="no-trusted-plugins">
        暂无信任的插件
      </div>
      
      <div v-else class="trusted-plugins-list">
        <div v-for="plugin in trustedPlugins" :key="plugin" class="trusted-plugin-item">
          <span class="plugin-name">{{ plugin }}</span>
          <button @click="removeTrustedPlugin(plugin)" class="remove-btn">移除</button>
        </div>
      </div>
    </div>
    
    <!-- 权限说明 -->
    <div class="permissions-info">
      <h3>权限说明</h3>
      <div class="permissions-grid">
        <div class="permission-item">
          <strong>fs</strong> - 文件系统访问
          <p>允许插件读写文件和目录</p>
        </div>
        <div class="permission-item">
          <strong>dialog</strong> - 对话框
          <p>允许插件显示系统对话框</p>
        </div>
        <div class="permission-item">
          <strong>notification</strong> - 通知
          <p>允许插件显示系统通知</p>
        </div>
        <div class="permission-item">
          <strong>shell</strong> - 外部应用
          <p>允许插件打开外部应用和链接</p>
        </div>
        <div class="permission-item">
          <strong>exec</strong> - 命令执行
          <p>允许插件执行系统命令</p>
        </div>
        <div class="permission-item">
          <strong>window</strong> - 窗口管理
          <p>允许插件创建和管理窗口</p>
        </div>
        <div class="permission-item">
          <strong>storage</strong> - 数据存储
          <p>允许插件存储和读取数据</p>
        </div>
        <div class="permission-item">
          <strong>events</strong> - 事件系统
          <p>允许插件发送和接收事件</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';

const ipcRenderer = (window as any).ipcRenderer;

export default defineComponent({
  name: 'SecuritySettings',
  setup() {
    const loading = ref(false);
    const loadingMessage = ref('');
    const error = ref('');
    const successMessage = ref('');
    
    const policy = ref({
      enablePermissionCheck: true,
      enableCodeSigning: false,
      enableSandbox: false,
      maxFileSize: 10485760, // 10MB in bytes
      allowedExtensions: ['.js', '.mjs'],
      blockedModules: ['child_process', 'fs', 'path', 'os', 'crypto']
    });
    
    const maxFileSizeMB = ref(10);
    const trustedPlugins = ref<string[]>([]);
    const newTrustedPlugin = ref('');

    onMounted(() => {
      loadSecurityPolicy();
    });

    const loadSecurityPolicy = async () => {
      try {
        loading.value = true;
        loadingMessage.value = '加载安全策略...';
        
        const currentPolicy = await ipcRenderer.invoke('get-security-policy');
        policy.value = currentPolicy;
        maxFileSizeMB.value = Math.round(currentPolicy.maxFileSize / (1024 * 1024));
      } catch (err) {
        error.value = err instanceof Error ? err.message : '加载安全策略失败';
      } finally {
        loading.value = false;
        loadingMessage.value = '';
      }
    };

    const updatePolicy = async () => {
      try {
        clearMessages();
        await ipcRenderer.invoke('update-security-policy', policy.value);
        successMessage.value = '安全策略已更新';
      } catch (err) {
        error.value = err instanceof Error ? err.message : '更新安全策略失败';
      }
    };

    const updateMaxFileSize = async () => {
      policy.value.maxFileSize = maxFileSizeMB.value * 1024 * 1024;
      await updatePolicy();
    };

    const addTrustedPlugin = async () => {
      const pluginName = newTrustedPlugin.value.trim();
      if (!pluginName) return;

      try {
        clearMessages();
        await ipcRenderer.invoke('add-trusted-plugin', pluginName);
        trustedPlugins.value.push(pluginName);
        newTrustedPlugin.value = '';
        successMessage.value = `插件 "${pluginName}" 已添加到信任列表`;
      } catch (err) {
        error.value = err instanceof Error ? err.message : '添加信任插件失败';
      }
    };

    const removeTrustedPlugin = async (pluginName: string) => {
      try {
        clearMessages();
        await ipcRenderer.invoke('remove-trusted-plugin', pluginName);
        const index = trustedPlugins.value.indexOf(pluginName);
        if (index > -1) {
          trustedPlugins.value.splice(index, 1);
        }
        successMessage.value = `插件 "${pluginName}" 已从信任列表移除`;
      } catch (err) {
        error.value = err instanceof Error ? err.message : '移除信任插件失败';
      }
    };

    const clearMessages = () => {
      error.value = '';
      successMessage.value = '';
    };

    const clearError = () => {
      error.value = '';
    };

    const clearSuccess = () => {
      successMessage.value = '';
    };

    return {
      loading,
      loadingMessage,
      error,
      successMessage,
      policy,
      maxFileSizeMB,
      trustedPlugins,
      newTrustedPlugin,
      updatePolicy,
      updateMaxFileSize,
      addTrustedPlugin,
      removeTrustedPlugin,
      clearError,
      clearSuccess,
    };
  },
});
</script>

<style scoped>
.security-settings {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.security-settings h2 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.security-settings h3 {
  color: #555;
  margin-top: 30px;
  margin-bottom: 15px;
}

/* 消息样式 */
.error-message, .success-message, .loading-message {
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message {
  background-color: #fee;
  border: 1px solid #fcc;
  color: #c33;
}

.success-message {
  background-color: #efe;
  border: 1px solid #cfc;
  color: #363;
}

.loading-message {
  background-color: #f0f8ff;
  border: 1px solid #b3d9ff;
  color: #0066cc;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #b3d9ff;
  border-top: 2px solid #0066cc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
  opacity: 0.7;
}

.close-btn:hover {
  opacity: 1;
}

/* 策略设置样式 */
.policy-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.policy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.policy-item label {
  display: flex;
  align-items: center;
  font-weight: 500;
  margin-bottom: 5px;
}

.policy-item input[type="checkbox"] {
  margin-right: 8px;
}

.policy-item input[type="number"] {
  margin-left: 8px;
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 80px;
}

.policy-description {
  font-size: 12px;
  color: #666;
  margin: 5px 0 0 0;
  line-height: 1.4;
}

/* 信任插件样式 */
.trusted-plugins-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.section-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 15px;
}

.add-trusted-plugin {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.add-trusted-plugin input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.add-trusted-plugin button {
  padding: 8px 16px;
  background-color: #667eea;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.add-trusted-plugin button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.no-trusted-plugins {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

.trusted-plugins-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.trusted-plugin-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.plugin-name {
  font-weight: 500;
}

.remove-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.remove-btn:hover {
  background-color: #c82333;
}

/* 权限说明样式 */
.permissions-info {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.permission-item {
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.permission-item strong {
  color: #667eea;
  font-family: monospace;
}

.permission-item p {
  margin: 5px 0 0 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}
</style>
