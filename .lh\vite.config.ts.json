{"sourceFile": "vite.config.ts", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750233524783, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750233524783, "name": "Commit-0", "content": "import { defineConfig } from 'vite'\nimport path from 'node:path'\nimport electron from 'vite-plugin-electron/simple'\nimport vue from '@vitejs/plugin-vue'\n\n// https://vitejs.dev/config/\nexport default defineConfig({\n  plugins: [\n    vue(),\n    electron({\n      main: {\n        // Shortcut of `build.lib.entry`.\n        entry: 'electron/main.ts',\n        onstart({ startup }) {\n        startup([\n          '.',\n          '--no-sandbox',\n          '--sourcemap',\n          // For Chrome devtools\n          '--remote-debugging-port=9222',\n        ])\n      },\n      },\n      preload: {\n        // Shortcut of `build.rollupOptions.input`.\n        // Preload scripts may contain Web assets, so use the `build.rollupOptions.input` instead `build.lib.entry`.\n        input: path.join(__dirname, 'electron/preload.ts'),\n      },\n      // Ployfill the Electron and Node.js API for Renderer process.\n      // If you want use Node.js in Renderer process, the `nodeIntegration` needs to be enabled in the Main process.\n      // See 👉 https://github.com/electron-vite/vite-plugin-electron-renderer\n      renderer: process.env.NODE_ENV === 'test'\n        // https://github.com/electron-vite/vite-plugin-electron-renderer/issues/78#issuecomment-2053600808\n        ? undefined\n        : {},\n    }),\n  ],\n})\n"}]}