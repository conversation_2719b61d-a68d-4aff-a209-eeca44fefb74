var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
import { BrowserWindow, shell, app, Notification, dialog, ipcMain } from "electron";
import { fileURLToPath } from "node:url";
import path$1 from "node:path";
import fs from "fs";
import path from "path";
import { exec } from "child_process";
import { promisify } from "util";
import crypto from "crypto";
const execAsync = promisify(exec);
class PluginStorage {
  constructor(pluginName) {
    __publicField(this, "storage", /* @__PURE__ */ new Map());
    __publicField(this, "pluginName");
    this.pluginName = pluginName;
    this.loadFromFile();
  }
  getStorageFile() {
    const appDataPath = app.getPath("userData");
    const pluginDataDir = path.join(appDataPath, "plugin-data");
    if (!fs.existsSync(pluginDataDir)) {
      fs.mkdirSync(pluginDataDir, { recursive: true });
    }
    return path.join(pluginDataDir, `${this.pluginName}.json`);
  }
  loadFromFile() {
    try {
      const storageFile = this.getStorageFile();
      if (fs.existsSync(storageFile)) {
        const data = JSON.parse(fs.readFileSync(storageFile, "utf-8"));
        this.storage = new Map(Object.entries(data));
      }
    } catch (error) {
      console.warn(`Failed to load storage for plugin ${this.pluginName}:`, error);
    }
  }
  saveToFile() {
    try {
      const storageFile = this.getStorageFile();
      const data = Object.fromEntries(this.storage);
      fs.writeFileSync(storageFile, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Failed to save storage for plugin ${this.pluginName}:`, error);
    }
  }
  get(key) {
    return this.storage.get(key);
  }
  set(key, value) {
    this.storage.set(key, value);
    this.saveToFile();
  }
  remove(key) {
    this.storage.delete(key);
    this.saveToFile();
  }
  clear() {
    this.storage.clear();
    this.saveToFile();
  }
}
class PluginEventManager {
  constructor() {
    __publicField(this, "listeners", /* @__PURE__ */ new Map());
  }
  emit(eventName, ...args) {
    const eventListeners = this.listeners.get(eventName);
    if (eventListeners) {
      eventListeners.forEach((listener) => {
        try {
          listener(...args);
        } catch (error) {
          console.error(`Error in event listener for ${eventName}:`, error);
        }
      });
    }
  }
  on(eventName, listener) {
    if (!this.listeners.has(eventName)) {
      this.listeners.set(eventName, /* @__PURE__ */ new Set());
    }
    this.listeners.get(eventName).add(listener);
  }
  off(eventName, listener) {
    if (!listener) {
      this.listeners.delete(eventName);
    } else {
      const eventListeners = this.listeners.get(eventName);
      if (eventListeners) {
        eventListeners.delete(listener);
        if (eventListeners.size === 0) {
          this.listeners.delete(eventName);
        }
      }
    }
  }
  once(eventName, listener) {
    const onceListener = (...args) => {
      listener(...args);
      this.off(eventName, onceListener);
    };
    this.on(eventName, onceListener);
  }
}
const globalEventManager = new PluginEventManager();
function createPluginAPI(pluginName) {
  const storage = new PluginStorage(pluginName);
  return {
    system: {
      getVersion: () => process.version,
      getPlatform: () => process.platform,
      getArch: () => process.arch,
      getAppVersion: () => app.getVersion()
    },
    fs: {
      readFile: (filePath) => fs.promises.readFile(filePath, "utf-8"),
      writeFile: (filePath, content) => fs.promises.writeFile(filePath, content, "utf-8"),
      exists: (filePath) => fs.existsSync(filePath),
      mkdir: (dirPath) => fs.promises.mkdir(dirPath, { recursive: true }),
      readdir: (dirPath) => fs.promises.readdir(dirPath),
      stat: (filePath) => fs.promises.stat(filePath)
    },
    dialog: {
      showMessageBox: async (options) => {
        const result = await dialog.showMessageBox(options);
        return result;
      },
      showOpenDialog: async (options) => {
        const result = await dialog.showOpenDialog(options);
        return result;
      },
      showSaveDialog: async (options) => {
        const result = await dialog.showSaveDialog(options);
        return result;
      }
    },
    notification: {
      show: (options) => {
        const notification = new Notification(options);
        notification.show();
      }
    },
    shell: {
      openExternal: (url) => shell.openExternal(url),
      openPath: (path2) => shell.openPath(path2),
      showItemInFolder: (fullPath) => shell.showItemInFolder(fullPath)
    },
    exec: {
      run: async (command, options = {}) => {
        const result = await execAsync(command, options);
        return result;
      }
    },
    window: {
      getCurrentWindow: () => BrowserWindow.getFocusedWindow(),
      createWindow: (options) => {
        const win2 = new BrowserWindow({
          width: options.width || 800,
          height: options.height || 600,
          title: options.title || "Plugin Window",
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true
          }
        });
        if (options.url) {
          win2.loadURL(options.url);
        }
        return win2;
      },
      closeWindow: (windowId) => {
        const win2 = windowId ? BrowserWindow.fromId(windowId) : BrowserWindow.getFocusedWindow();
        if (win2) {
          win2.close();
        }
      }
    },
    logger: {
      info: (message, ...args) => console.log(`[${pluginName}] INFO:`, message, ...args),
      warn: (message, ...args) => console.warn(`[${pluginName}] WARN:`, message, ...args),
      error: (message, ...args) => console.error(`[${pluginName}] ERROR:`, message, ...args),
      debug: (message, ...args) => console.debug(`[${pluginName}] DEBUG:`, message, ...args)
    },
    storage: {
      get: (key) => storage.get(key),
      set: (key, value) => storage.set(key, value),
      remove: (key) => storage.remove(key),
      clear: () => storage.clear()
    },
    events: {
      emit: (eventName, ...args) => globalEventManager.emit(eventName, ...args),
      on: (eventName, listener) => globalEventManager.on(eventName, listener),
      off: (eventName, listener) => globalEventManager.off(eventName, listener),
      once: (eventName, listener) => globalEventManager.once(eventName, listener)
    }
  };
}
var PluginPermission = /* @__PURE__ */ ((PluginPermission2) => {
  PluginPermission2["FILE_SYSTEM"] = "fs";
  PluginPermission2["DIALOG"] = "dialog";
  PluginPermission2["NOTIFICATION"] = "notification";
  PluginPermission2["SHELL"] = "shell";
  PluginPermission2["EXEC"] = "exec";
  PluginPermission2["WINDOW"] = "window";
  PluginPermission2["STORAGE"] = "storage";
  PluginPermission2["EVENTS"] = "events";
  PluginPermission2["NETWORK"] = "network";
  return PluginPermission2;
})(PluginPermission || {});
const DEFAULT_SECURITY_POLICY = {
  enablePermissionCheck: true,
  enableCodeSigning: false,
  // 在生产环境中应该启用
  maxFileSize: 10 * 1024 * 1024,
  // 10MB
  allowedExtensions: [".js", ".mjs"],
  blockedModules: ["child_process", "fs", "path", "os", "crypto"],
  enableSandbox: false
  // 在生产环境中应该启用
};
class PluginSecurityManager {
  constructor(policy = DEFAULT_SECURITY_POLICY) {
    __publicField(this, "policy");
    __publicField(this, "trustedPlugins", /* @__PURE__ */ new Set());
    this.policy = policy;
  }
  /**
   * 验证插件清单
   */
  validateManifest(manifest) {
    const errors = [];
    if (!manifest.name || typeof manifest.name !== "string") {
      errors.push("Plugin name is required and must be a string");
    }
    if (!manifest.main || typeof manifest.main !== "string") {
      errors.push("Plugin main entry is required and must be a string");
    }
    if (!manifest.description || typeof manifest.description !== "string") {
      errors.push("Plugin description is required and must be a string");
    }
    if (manifest.version && !/^\d+\.\d+\.\d+/.test(manifest.version)) {
      errors.push("Plugin version must follow semantic versioning (x.y.z)");
    }
    if (manifest.permissions) {
      if (!Array.isArray(manifest.permissions)) {
        errors.push("Plugin permissions must be an array");
      } else {
        const validPermissions = Object.values(PluginPermission);
        const invalidPermissions = manifest.permissions.filter(
          (perm) => !validPermissions.includes(perm)
        );
        if (invalidPermissions.length > 0) {
          errors.push(`Invalid permissions: ${invalidPermissions.join(", ")}`);
        }
      }
    }
    if (manifest.name && !/^[a-zA-Z0-9_-]+$/.test(manifest.name)) {
      errors.push("Plugin name can only contain letters, numbers, underscores, and hyphens");
    }
    return { valid: errors.length === 0, errors };
  }
  /**
   * 验证插件文件
   */
  async validatePluginFile(filePath) {
    const errors = [];
    try {
      if (!fs.existsSync(filePath)) {
        errors.push("Plugin file does not exist");
        return { valid: false, errors };
      }
      const stats = fs.statSync(filePath);
      if (stats.size > this.policy.maxFileSize) {
        errors.push(`Plugin file size (${stats.size}) exceeds maximum allowed size (${this.policy.maxFileSize})`);
      }
      const ext = path.extname(filePath);
      if (!this.policy.allowedExtensions.includes(ext)) {
        errors.push(`Plugin file extension '${ext}' is not allowed`);
      }
      const content = fs.readFileSync(filePath, "utf-8");
      for (const blockedModule of this.policy.blockedModules) {
        const importRegex = new RegExp(`require\\s*\\(\\s*['"\`]${blockedModule}['"\`]\\s*\\)`, "g");
        const importRegex2 = new RegExp(`from\\s+['"\`]${blockedModule}['"\`]`, "g");
        if (importRegex.test(content) || importRegex2.test(content)) {
          errors.push(`Plugin contains blocked module import: ${blockedModule}`);
        }
      }
      const dangerousFunctions = ["eval", "Function", "setTimeout", "setInterval"];
      for (const func of dangerousFunctions) {
        const funcRegex = new RegExp(`\\b${func}\\s*\\(`, "g");
        if (funcRegex.test(content)) {
          errors.push(`Plugin contains potentially dangerous function: ${func}`);
        }
      }
    } catch (error) {
      errors.push(`Error validating plugin file: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
    return { valid: errors.length === 0, errors };
  }
  /**
   * 检查插件权限
   */
  checkPermission(pluginName, permission, manifest) {
    if (!this.policy.enablePermissionCheck) {
      return true;
    }
    if (this.trustedPlugins.has(pluginName)) {
      return true;
    }
    if (manifest && manifest.permissions) {
      return manifest.permissions.includes(permission);
    }
    return false;
  }
  /**
   * 添加信任的插件
   */
  addTrustedPlugin(pluginName) {
    this.trustedPlugins.add(pluginName);
  }
  /**
   * 移除信任的插件
   */
  removeTrustedPlugin(pluginName) {
    this.trustedPlugins.delete(pluginName);
  }
  /**
   * 生成插件签名
   */
  generatePluginSignature(pluginPath) {
    const content = fs.readFileSync(pluginPath);
    return crypto.createHash("sha256").update(content).digest("hex");
  }
  /**
   * 验证插件签名
   */
  verifyPluginSignature(pluginPath, expectedSignature) {
    if (!this.policy.enableCodeSigning) {
      return true;
    }
    const actualSignature = this.generatePluginSignature(pluginPath);
    return actualSignature === expectedSignature;
  }
  /**
   * 创建安全的插件API代理
   */
  createSecureAPIProxy(pluginName, api, manifest) {
    const secureAPI = {};
    for (const [moduleName, moduleAPI] of Object.entries(api)) {
      const permission = moduleName;
      if (this.checkPermission(pluginName, permission, manifest)) {
        secureAPI[moduleName] = moduleAPI;
      } else {
        secureAPI[moduleName] = new Proxy({}, {
          get: () => {
            throw new Error(`Plugin "${pluginName}" does not have permission to access "${moduleName}"`);
          }
        });
      }
    }
    return secureAPI;
  }
  /**
   * 更新安全策略
   */
  updatePolicy(newPolicy) {
    this.policy = { ...this.policy, ...newPolicy };
  }
  /**
   * 获取当前安全策略
   */
  getPolicy() {
    return { ...this.policy };
  }
}
const securityManager = new PluginSecurityManager();
const __dirname = path$1.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = path$1.join(__dirname, "..");
const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
const MAIN_DIST = path$1.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path$1.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path$1.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
let win;
function createWindow() {
  win = new BrowserWindow({
    icon: path$1.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      preload: path$1.join(__dirname, "preload.mjs")
    }
  });
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
  } else {
    win.loadFile(path$1.join(RENDERER_DIST, "index.html"));
  }
}
const pluginFeatures = {};
const pluginLoadStatus = /* @__PURE__ */ new Map();
const pluginWatchers = /* @__PURE__ */ new Map();
let hotReloadEnabled = process.env.NODE_ENV === "development";
async function reloadPlugin(pluginName, pluginPath) {
  try {
    console.log(`Hot reloading plugin: ${pluginName}`);
    if (pluginFeatures[pluginName]) {
      try {
        const manifestPath = path$1.join(pluginPath, "manifest.json");
        if (fs.existsSync(manifestPath)) {
          const manifest = JSON.parse(fs.readFileSync(manifestPath, "utf-8"));
          const pluginEntry = path$1.join(pluginPath, manifest.main);
          delete require.cache[require.resolve(pluginEntry)];
          const pluginEntryUrl = `file://${pluginEntry.replace(/\\/g, "/")}`;
          const oldPlugin = await import(pluginEntryUrl);
          if (typeof oldPlugin.deactivate === "function") {
            await oldPlugin.deactivate();
          }
        }
      } catch (deactivateError) {
        console.warn(`Warning: Failed to deactivate plugin "${pluginName}":`, deactivateError);
      }
      delete pluginFeatures[pluginName];
    }
    await loadSinglePlugin(pluginName, pluginPath);
    if (win && !win.isDestroyed()) {
      win.webContents.send("plugin-reloaded", { pluginName, status: "success" });
    }
  } catch (error) {
    console.error(`Failed to reload plugin "${pluginName}":`, error);
    pluginLoadStatus.set(pluginName, {
      name: pluginName,
      status: "failed",
      error: error instanceof Error ? error.message : "Hot reload failed"
    });
    if (win && !win.isDestroyed()) {
      win.webContents.send("plugin-reloaded", {
        pluginName,
        status: "error",
        error: error instanceof Error ? error.message : "Hot reload failed"
      });
    }
  }
}
async function loadSinglePlugin(_pluginName, pluginPath) {
  const manifestPath = path$1.join(pluginPath, "manifest.json");
  if (!fs.existsSync(manifestPath)) {
    throw new Error(`Manifest file not found: ${manifestPath}`);
  }
  const manifestContent = fs.readFileSync(manifestPath, "utf-8");
  let manifest;
  try {
    manifest = JSON.parse(manifestContent);
  } catch (parseError) {
    const errorMessage = parseError instanceof Error ? parseError.message : "Invalid JSON format";
    throw new Error(`Invalid JSON in manifest: ${errorMessage}`);
  }
  const manifestValidation = securityManager.validateManifest(manifest);
  if (!manifestValidation.valid) {
    throw new Error(`Invalid manifest: ${manifestValidation.errors.join(", ")}`);
  }
  pluginLoadStatus.set(manifest.name, { name: manifest.name, status: "loading" });
  const pluginEntry = path$1.join(pluginPath, manifest.main);
  if (!fs.existsSync(pluginEntry)) {
    throw new Error(`Plugin entry file not found: ${pluginEntry}`);
  }
  const fileValidation = await securityManager.validatePluginFile(pluginEntry);
  if (!fileValidation.valid) {
    throw new Error(`Plugin file validation failed: ${fileValidation.errors.join(", ")}`);
  }
  const pluginEntryUrl = `file://${pluginEntry.replace(/\\/g, "/")}`;
  const plugin = await import(pluginEntryUrl);
  if (typeof plugin.activate !== "function") {
    throw new Error('Plugin must export an "activate" function');
  }
  const rawPluginAPI = createPluginAPI(manifest.name);
  const securePluginAPI = securityManager.createSecureAPIProxy(manifest.name, rawPluginAPI, manifest);
  const features = await plugin.activate(securePluginAPI);
  if (features && typeof features === "object") {
    pluginFeatures[manifest.name] = features;
    pluginLoadStatus.set(manifest.name, {
      name: manifest.name,
      status: "loaded",
      loadedAt: Date.now()
    });
    if (hotReloadEnabled) {
      setupHotReload(manifest.name, pluginPath);
    }
    console.log(`Plugin "${manifest.name}" loaded successfully`);
  } else {
    throw new Error("Plugin activate function must return an object with features");
  }
}
function setupHotReload(pluginName, pluginPath) {
  var _a;
  if (pluginWatchers.has(pluginName)) {
    (_a = pluginWatchers.get(pluginName)) == null ? void 0 : _a.close();
  }
  try {
    const watcher = fs.watch(pluginPath, { recursive: true }, (_, filename) => {
      if (filename && (filename.endsWith(".js") || filename.endsWith(".json"))) {
        console.log(`Plugin file changed: ${filename} in ${pluginName}`);
        setTimeout(() => {
          reloadPlugin(pluginName, pluginPath);
        }, 500);
      }
    });
    pluginWatchers.set(pluginName, watcher);
    console.log(`Hot reload enabled for plugin: ${pluginName}`);
  } catch (error) {
    console.warn(`Failed to setup hot reload for plugin "${pluginName}":`, error);
  }
}
async function loadPlugins() {
  const pluginsDir = path$1.join(process.env.APP_ROOT, "plugins");
  if (!fs.existsSync(pluginsDir)) {
    console.log("Plugins directory not found, creating...");
    fs.mkdirSync(pluginsDir, { recursive: true });
    return;
  }
  try {
    const pluginFolders = fs.readdirSync(pluginsDir);
    const loadPromises = pluginFolders.map(async (folder) => {
      const pluginPath = path$1.join(pluginsDir, folder);
      if (!fs.statSync(pluginPath).isDirectory()) {
        return;
      }
      try {
        await loadSinglePlugin(folder, pluginPath);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Unknown error";
        console.error(`Failed to load plugin "${folder}":`, errorMessage);
        pluginLoadStatus.set(folder, {
          name: folder,
          status: "failed",
          error: errorMessage
        });
      }
    });
    await Promise.allSettled(loadPromises);
    console.log(`Plugin loading completed. Loaded: ${Object.keys(pluginFeatures).length} plugins`);
  } catch (error) {
    console.error("Error reading plugins directory:", error);
  }
}
ipcMain.handle("get-plugins", async () => {
  try {
    const pluginsDir = path$1.join(process.env.APP_ROOT, "plugins");
    if (!fs.existsSync(pluginsDir)) return [];
    const pluginFolders = fs.readdirSync(pluginsDir);
    const plugins = [];
    for (const folder of pluginFolders) {
      const pluginPath = path$1.join(pluginsDir, folder);
      if (!fs.statSync(pluginPath).isDirectory()) {
        continue;
      }
      const manifestPath = path$1.join(pluginPath, "manifest.json");
      try {
        if (fs.existsSync(manifestPath)) {
          const manifestContent = fs.readFileSync(manifestPath, "utf-8");
          const manifest = JSON.parse(manifestContent);
          const loadStatus = pluginLoadStatus.get(manifest.name || folder);
          plugins.push({
            name: manifest.name || folder,
            description: manifest.description || "No description",
            version: manifest.version || "1.0.0",
            status: (loadStatus == null ? void 0 : loadStatus.status) || "unknown",
            error: loadStatus == null ? void 0 : loadStatus.error,
            author: manifest.author,
            license: manifest.license
          });
        }
      } catch (error) {
        console.error(`Error parsing plugin ${folder}:`, error);
        plugins.push({
          name: folder,
          description: "Error loading plugin",
          version: "unknown",
          status: "failed",
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }
    }
    return plugins;
  } catch (error) {
    console.error("Error getting plugins list:", error);
    throw new Error("Failed to get plugins list");
  }
});
ipcMain.handle("install-plugin", async (_, pluginPath) => {
  try {
    if (!pluginPath || typeof pluginPath !== "string") {
      throw new Error("Invalid plugin path provided");
    }
    if (!fs.existsSync(pluginPath)) {
      throw new Error("Plugin file does not exist");
    }
    const fileValidation = await securityManager.validatePluginFile(pluginPath);
    if (!fileValidation.valid) {
      throw new Error(`Plugin security validation failed: ${fileValidation.errors.join(", ")}`);
    }
    const pluginsDir = path$1.join(process.env.APP_ROOT, "plugins");
    if (!fs.existsSync(pluginsDir)) {
      fs.mkdirSync(pluginsDir, { recursive: true });
    }
    const pluginName = path$1.basename(pluginPath, path$1.extname(pluginPath));
    const targetDir = path$1.join(pluginsDir, pluginName);
    if (fs.existsSync(targetDir)) {
      throw new Error(`Plugin "${pluginName}" already exists`);
    }
    fs.mkdirSync(targetDir, { recursive: true });
    try {
      fs.copyFileSync(pluginPath, path$1.join(targetDir, path$1.basename(pluginPath)));
      await loadPlugins();
      return {
        success: true,
        message: `Plugin "${pluginName}" installed successfully`
      };
    } catch (copyError) {
      if (fs.existsSync(targetDir)) {
        fs.rmSync(targetDir, { recursive: true, force: true });
      }
      throw copyError;
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Unknown installation error";
    console.error("Plugin installation failed:", errorMessage);
    throw new Error(`Installation failed: ${errorMessage}`);
  }
});
ipcMain.handle("uninstall-plugin", async (_, pluginName) => {
  try {
    if (!pluginName || typeof pluginName !== "string") {
      throw new Error("Invalid plugin name provided");
    }
    const pluginsDir = path$1.join(process.env.APP_ROOT, "plugins");
    const targetDir = path$1.join(pluginsDir, pluginName);
    if (!fs.existsSync(targetDir)) {
      throw new Error(`Plugin "${pluginName}" not found`);
    }
    if (!fs.statSync(targetDir).isDirectory()) {
      throw new Error(`"${pluginName}" is not a valid plugin directory`);
    }
    try {
      if (pluginFeatures[pluginName]) {
        const manifestPath = path$1.join(targetDir, "manifest.json");
        if (fs.existsSync(manifestPath)) {
          const manifest = JSON.parse(fs.readFileSync(manifestPath, "utf-8"));
          const pluginEntry = path$1.join(targetDir, manifest.main);
          if (fs.existsSync(pluginEntry)) {
            const pluginEntryUrl = `file://${pluginEntry.replace(/\\/g, "/")}`;
            const plugin = await import(pluginEntryUrl);
            if (typeof plugin.deactivate === "function") {
              await plugin.deactivate();
            }
          }
        }
        delete pluginFeatures[pluginName];
        pluginLoadStatus.delete(pluginName);
      }
    } catch (deactivateError) {
      console.warn(`Warning: Failed to properly deactivate plugin "${pluginName}":`, deactivateError);
    }
    fs.rmSync(targetDir, { recursive: true, force: true });
    return {
      success: true,
      message: `Plugin "${pluginName}" uninstalled successfully`
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Unknown uninstallation error";
    console.error("Plugin uninstallation failed:", errorMessage);
    throw new Error(`Uninstallation failed: ${errorMessage}`);
  }
});
ipcMain.handle("execute-plugin-feature", async (_, pluginName, featureName, ...args) => {
  try {
    if (!pluginName || !featureName) {
      throw new Error("Plugin name and feature name are required");
    }
    if (!pluginFeatures[pluginName]) {
      throw new Error(`Plugin "${pluginName}" not found or not loaded`);
    }
    if (!pluginFeatures[pluginName][featureName]) {
      const availableFeatures = Object.keys(pluginFeatures[pluginName]);
      throw new Error(`Feature "${featureName}" not found in plugin "${pluginName}". Available features: ${availableFeatures.join(", ")}`);
    }
    const feature = pluginFeatures[pluginName][featureName];
    if (typeof feature !== "function") {
      throw new Error(`Feature "${featureName}" in plugin "${pluginName}" is not a function`);
    }
    const result = await feature(...args);
    return { success: true, result };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Unknown error executing plugin feature";
    console.error("Plugin feature execution failed:", errorMessage);
    return { success: false, error: errorMessage };
  }
});
ipcMain.handle("get-plugin-status", () => {
  const statusArray = Array.from(pluginLoadStatus.values());
  return statusArray;
});
ipcMain.handle("get-security-policy", () => {
  return securityManager.getPolicy();
});
ipcMain.handle("update-security-policy", (_, newPolicy) => {
  securityManager.updatePolicy(newPolicy);
  return { success: true, message: "Security policy updated successfully" };
});
ipcMain.handle("add-trusted-plugin", (_, pluginName) => {
  securityManager.addTrustedPlugin(pluginName);
  return { success: true, message: `Plugin "${pluginName}" added to trusted list` };
});
ipcMain.handle("remove-trusted-plugin", (_, pluginName) => {
  securityManager.removeTrustedPlugin(pluginName);
  return { success: true, message: `Plugin "${pluginName}" removed from trusted list` };
});
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
    win = null;
  }
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
app.whenReady().then(async () => {
  createWindow();
  await loadPlugins();
});
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
