import { ipc<PERSON><PERSON>, app, BrowserWindow } from "electron";
import { fileURLToPath } from "node:url";
import path from "node:path";
import fs from "fs";
const __dirname = path.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = path.join(__dirname, "..");
const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
let win;
function createWindow() {
  win = new BrowserWindow({
    icon: path.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      preload: path.join(__dirname, "preload.mjs")
    }
  });
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
  } else {
    win.loadFile(path.join(RENDERER_DIST, "index.html"));
  }
}
const pluginFeatures = {};
const pluginLoadStatus = /* @__PURE__ */ new Map();
const pluginWatchers = /* @__PURE__ */ new Map();
let hotReloadEnabled = process.env.NODE_ENV === "development";
async function reloadPlugin(pluginName, pluginPath) {
  try {
    console.log(`Hot reloading plugin: ${pluginName}`);
    if (pluginFeatures[pluginName]) {
      try {
        const manifestPath = path.join(pluginPath, "manifest.json");
        if (fs.existsSync(manifestPath)) {
          const manifest = JSON.parse(fs.readFileSync(manifestPath, "utf-8"));
          const pluginEntry = path.join(pluginPath, manifest.main);
          delete require.cache[require.resolve(pluginEntry)];
          const oldPlugin = await import(pluginEntry);
          if (typeof oldPlugin.deactivate === "function") {
            await oldPlugin.deactivate();
          }
        }
      } catch (deactivateError) {
        console.warn(`Warning: Failed to deactivate plugin "${pluginName}":`, deactivateError);
      }
      delete pluginFeatures[pluginName];
    }
    await loadSinglePlugin(pluginName, pluginPath);
    if (win && !win.isDestroyed()) {
      win.webContents.send("plugin-reloaded", { pluginName, status: "success" });
    }
  } catch (error) {
    console.error(`Failed to reload plugin "${pluginName}":`, error);
    pluginLoadStatus.set(pluginName, {
      name: pluginName,
      status: "failed",
      error: error instanceof Error ? error.message : "Hot reload failed"
    });
    if (win && !win.isDestroyed()) {
      win.webContents.send("plugin-reloaded", {
        pluginName,
        status: "error",
        error: error instanceof Error ? error.message : "Hot reload failed"
      });
    }
  }
}
async function loadSinglePlugin(pluginName, pluginPath) {
  const manifestPath = path.join(pluginPath, "manifest.json");
  if (!fs.existsSync(manifestPath)) {
    throw new Error(`Manifest file not found: ${manifestPath}`);
  }
  const manifestContent = fs.readFileSync(manifestPath, "utf-8");
  let manifest;
  try {
    manifest = JSON.parse(manifestContent);
  } catch (parseError) {
    const errorMessage = parseError instanceof Error ? parseError.message : "Invalid JSON format";
    throw new Error(`Invalid JSON in manifest: ${errorMessage}`);
  }
  if (!manifest.name || !manifest.main) {
    throw new Error('Manifest must contain "name" and "main" fields');
  }
  pluginLoadStatus.set(manifest.name, { name: manifest.name, status: "loading" });
  const pluginEntry = path.join(pluginPath, manifest.main);
  if (!fs.existsSync(pluginEntry)) {
    throw new Error(`Plugin entry file not found: ${pluginEntry}`);
  }
  const plugin = await import(pluginEntry);
  if (typeof plugin.activate !== "function") {
    throw new Error('Plugin must export an "activate" function');
  }
  const features = await plugin.activate();
  if (features && typeof features === "object") {
    pluginFeatures[manifest.name] = features;
    pluginLoadStatus.set(manifest.name, {
      name: manifest.name,
      status: "loaded",
      loadedAt: Date.now()
    });
    if (hotReloadEnabled) {
      setupHotReload(manifest.name, pluginPath);
    }
    console.log(`Plugin "${manifest.name}" loaded successfully`);
  } else {
    throw new Error("Plugin activate function must return an object with features");
  }
}
function setupHotReload(pluginName, pluginPath) {
  var _a;
  if (pluginWatchers.has(pluginName)) {
    (_a = pluginWatchers.get(pluginName)) == null ? void 0 : _a.close();
  }
  try {
    const watcher = fs.watch(pluginPath, { recursive: true }, (_, filename) => {
      if (filename && (filename.endsWith(".js") || filename.endsWith(".json"))) {
        console.log(`Plugin file changed: ${filename} in ${pluginName}`);
        setTimeout(() => {
          reloadPlugin(pluginName, pluginPath);
        }, 500);
      }
    });
    pluginWatchers.set(pluginName, watcher);
    console.log(`Hot reload enabled for plugin: ${pluginName}`);
  } catch (error) {
    console.warn(`Failed to setup hot reload for plugin "${pluginName}":`, error);
  }
}
async function loadPlugins() {
  const pluginsDir = path.join(process.env.APP_ROOT, "plugins");
  if (!fs.existsSync(pluginsDir)) {
    console.log("Plugins directory not found, creating...");
    fs.mkdirSync(pluginsDir, { recursive: true });
    return;
  }
  try {
    const pluginFolders = fs.readdirSync(pluginsDir);
    const loadPromises = pluginFolders.map(async (folder) => {
      const pluginPath = path.join(pluginsDir, folder);
      if (!fs.statSync(pluginPath).isDirectory()) {
        return;
      }
      const manifestPath = path.join(pluginPath, "manifest.json");
      try {
        await loadSinglePlugin(folder, pluginPath);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Unknown error";
        console.error(`Failed to load plugin "${folder}":`, errorMessage);
        pluginLoadStatus.set(folder, {
          name: folder,
          status: "failed",
          error: errorMessage
        });
      }
    });
    await Promise.allSettled(loadPromises);
    console.log(`Plugin loading completed. Loaded: ${Object.keys(pluginFeatures).length} plugins`);
  } catch (error) {
    console.error("Error reading plugins directory:", error);
  }
}
ipcMain.handle("get-plugins", async () => {
  try {
    const pluginsDir = path.join(process.env.APP_ROOT, "plugins");
    if (!fs.existsSync(pluginsDir)) return [];
    const pluginFolders = fs.readdirSync(pluginsDir);
    const plugins = [];
    for (const folder of pluginFolders) {
      const pluginPath = path.join(pluginsDir, folder);
      if (!fs.statSync(pluginPath).isDirectory()) {
        continue;
      }
      const manifestPath = path.join(pluginPath, "manifest.json");
      try {
        if (fs.existsSync(manifestPath)) {
          const manifestContent = fs.readFileSync(manifestPath, "utf-8");
          const manifest = JSON.parse(manifestContent);
          const loadStatus = pluginLoadStatus.get(manifest.name || folder);
          plugins.push({
            name: manifest.name || folder,
            description: manifest.description || "No description",
            version: manifest.version || "1.0.0",
            status: (loadStatus == null ? void 0 : loadStatus.status) || "unknown",
            error: loadStatus == null ? void 0 : loadStatus.error,
            author: manifest.author,
            license: manifest.license
          });
        }
      } catch (error) {
        console.error(`Error parsing plugin ${folder}:`, error);
        plugins.push({
          name: folder,
          description: "Error loading plugin",
          version: "unknown",
          status: "failed",
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }
    }
    return plugins;
  } catch (error) {
    console.error("Error getting plugins list:", error);
    throw new Error("Failed to get plugins list");
  }
});
ipcMain.handle("install-plugin", async (_, pluginPath) => {
  try {
    if (!pluginPath || typeof pluginPath !== "string") {
      throw new Error("Invalid plugin path provided");
    }
    if (!fs.existsSync(pluginPath)) {
      throw new Error("Plugin file does not exist");
    }
    const pluginsDir = path.join(process.env.APP_ROOT, "plugins");
    if (!fs.existsSync(pluginsDir)) {
      fs.mkdirSync(pluginsDir, { recursive: true });
    }
    const pluginName = path.basename(pluginPath, path.extname(pluginPath));
    const targetDir = path.join(pluginsDir, pluginName);
    if (fs.existsSync(targetDir)) {
      throw new Error(`Plugin "${pluginName}" already exists`);
    }
    fs.mkdirSync(targetDir, { recursive: true });
    try {
      fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));
      await loadPlugins();
      return {
        success: true,
        message: `Plugin "${pluginName}" installed successfully`
      };
    } catch (copyError) {
      if (fs.existsSync(targetDir)) {
        fs.rmSync(targetDir, { recursive: true, force: true });
      }
      throw copyError;
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Unknown installation error";
    console.error("Plugin installation failed:", errorMessage);
    throw new Error(`Installation failed: ${errorMessage}`);
  }
});
ipcMain.handle("uninstall-plugin", async (_, pluginName) => {
  try {
    if (!pluginName || typeof pluginName !== "string") {
      throw new Error("Invalid plugin name provided");
    }
    const pluginsDir = path.join(process.env.APP_ROOT, "plugins");
    const targetDir = path.join(pluginsDir, pluginName);
    if (!fs.existsSync(targetDir)) {
      throw new Error(`Plugin "${pluginName}" not found`);
    }
    if (!fs.statSync(targetDir).isDirectory()) {
      throw new Error(`"${pluginName}" is not a valid plugin directory`);
    }
    try {
      if (pluginFeatures[pluginName]) {
        const manifestPath = path.join(targetDir, "manifest.json");
        if (fs.existsSync(manifestPath)) {
          const manifest = JSON.parse(fs.readFileSync(manifestPath, "utf-8"));
          const pluginEntry = path.join(targetDir, manifest.main);
          if (fs.existsSync(pluginEntry)) {
            const plugin = await import(pluginEntry);
            if (typeof plugin.deactivate === "function") {
              await plugin.deactivate();
            }
          }
        }
        delete pluginFeatures[pluginName];
        pluginLoadStatus.delete(pluginName);
      }
    } catch (deactivateError) {
      console.warn(`Warning: Failed to properly deactivate plugin "${pluginName}":`, deactivateError);
    }
    fs.rmSync(targetDir, { recursive: true, force: true });
    return {
      success: true,
      message: `Plugin "${pluginName}" uninstalled successfully`
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Unknown uninstallation error";
    console.error("Plugin uninstallation failed:", errorMessage);
    throw new Error(`Uninstallation failed: ${errorMessage}`);
  }
});
ipcMain.handle("get-plugin-feature", async (_, pluginName, featureName) => {
  try {
    if (!pluginName || !featureName) {
      throw new Error("Plugin name and feature name are required");
    }
    if (!pluginFeatures[pluginName]) {
      throw new Error(`Plugin "${pluginName}" not found or not loaded`);
    }
    if (!pluginFeatures[pluginName][featureName]) {
      const availableFeatures = Object.keys(pluginFeatures[pluginName]);
      throw new Error(`Feature "${featureName}" not found in plugin "${pluginName}". Available features: ${availableFeatures.join(", ")}`);
    }
    const feature = pluginFeatures[pluginName][featureName];
    if (typeof feature !== "function") {
      throw new Error(`Feature "${featureName}" in plugin "${pluginName}" is not a function`);
    }
    return feature;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Unknown error accessing plugin feature";
    console.error("Plugin feature access failed:", errorMessage);
    throw new Error(errorMessage);
  }
});
ipcMain.handle("get-plugin-status", () => {
  const statusArray = Array.from(pluginLoadStatus.values());
  return statusArray;
});
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
    win = null;
  }
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
app.whenReady().then(async () => {
  createWindow();
  await loadPlugins();
});
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
