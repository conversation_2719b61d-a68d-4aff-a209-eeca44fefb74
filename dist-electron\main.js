import { ipc<PERSON><PERSON>, app, BrowserWindow } from "electron";
import { fileURLToPath } from "node:url";
import path from "node:path";
import fs from "fs";
const __dirname = path.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = path.join(__dirname, "..");
const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
let win;
function createWindow() {
  win = new BrowserWindow({
    icon: path.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      preload: path.join(__dirname, "preload.mjs")
    }
  });
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
  } else {
    win.loadFile(path.join(RENDERER_DIST, "index.html"));
  }
}
const pluginFeatures = {};
function loadPlugins() {
  const pluginsDir = path.join(process.env.APP_ROOT, "plugins");
  if (!fs.existsSync(pluginsDir)) return;
  const pluginFolders = fs.readdirSync(pluginsDir);
  pluginFolders.forEach((folder) => {
    const manifestPath = path.join(pluginsDir, folder, "manifest.json");
    if (fs.existsSync(manifestPath)) {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, "utf-8"));
      const pluginEntry = path.join(pluginsDir, folder, manifest.main);
      if (fs.existsSync(pluginEntry)) {
        import(pluginEntry).then((plugin) => {
          if (plugin.activate) {
            const features = plugin.activate();
            if (features) {
              pluginFeatures[manifest.name] = features;
            }
          }
        }).catch((err) => console.error(`Failed to load plugin ${folder}:`, err));
      }
    }
  });
}
ipcMain.handle("get-plugins", () => {
  const pluginsDir = path.join(process.env.APP_ROOT, "plugins");
  if (!fs.existsSync(pluginsDir)) return [];
  const pluginFolders = fs.readdirSync(pluginsDir);
  return pluginFolders.map((folder) => {
    const manifestPath = path.join(pluginsDir, folder, "manifest.json");
    if (fs.existsSync(manifestPath)) {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, "utf-8"));
      return {
        name: manifest.name,
        description: manifest.description
      };
    }
    return null;
  }).filter(Boolean);
});
ipcMain.handle("install-plugin", (_, pluginPath) => {
  const pluginsDir = path.join(process.env.APP_ROOT, "plugins");
  if (!fs.existsSync(pluginsDir)) {
    fs.mkdirSync(pluginsDir);
  }
  const pluginName = path.basename(pluginPath, path.extname(pluginPath));
  const targetDir = path.join(pluginsDir, pluginName);
  if (fs.existsSync(targetDir)) {
    throw new Error("Plugin already exists");
  }
  fs.mkdirSync(targetDir);
  fs.copyFileSync(pluginPath, path.join(targetDir, path.basename(pluginPath)));
  return true;
});
ipcMain.handle("uninstall-plugin", (_, pluginName) => {
  const pluginsDir = path.join(process.env.APP_ROOT, "plugins");
  const targetDir = path.join(pluginsDir, pluginName);
  if (fs.existsSync(targetDir)) {
    fs.rmSync(targetDir, { recursive: true, force: true });
    return true;
  }
  throw new Error("Plugin not found");
});
ipcMain.handle("get-plugin-feature", (_, pluginName, featureName) => {
  if (pluginFeatures[pluginName] && pluginFeatures[pluginName][featureName]) {
    return pluginFeatures[pluginName][featureName];
  }
  throw new Error(`Feature ${featureName} not found in plugin ${pluginName}`);
});
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
    win = null;
  }
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
app.whenReady().then(() => {
  createWindow();
  loadPlugins();
});
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
