{"sourceFile": "src/views/PluginManager.vue", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1750234829908, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750234899488, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -72,51 +72,136 @@\n // 为插件定义类型\r\n interface Plugin {\r\n   name: string;\r\n   description: string;\r\n+  version?: string;\r\n+  status: 'loading' | 'loaded' | 'failed' | 'unknown';\r\n+  error?: string;\r\n }\r\n \r\n+const ipcRenderer = (window as any).ipcRenderer;\r\n+\r\n export default defineComponent({\r\n   name: 'PluginManager',\r\n   setup() {\r\n-    // 修改 plugins 的类型\r\n     const plugins = ref<Plugin[]>([]);\r\n+    const loading = ref(false);\r\n+    const loadingMessage = ref('');\r\n+    const error = ref('');\r\n+    const successMessage = ref('');\r\n \r\n     onMounted(() => {\r\n       loadPlugins();\r\n     });\r\n \r\n-    const loadPlugins = () => {\r\n-      // 为 loadedPlugins 显式指定类型\r\n-      ipc<PERSON>enderer.invoke('get-plugins').then((loadedPlugins: Plugin[]) => {\r\n+    const loadPlugins = async () => {\r\n+      try {\r\n+        loading.value = true;\r\n+        loadingMessage.value = '加载插件列表...';\r\n+        clearMessages();\r\n+\r\n+        const loadedPlugins: Plugin[] = await ipcRenderer.invoke('get-plugins');\r\n         plugins.value = loadedPlugins;\r\n-      });\r\n+      } catch (err) {\r\n+        const errorMessage = err instanceof Error ? err.message : '加载插件列表失败';\r\n+        error.value = errorMessage;\r\n+        console.error('Failed to load plugins:', err);\r\n+      } finally {\r\n+        loading.value = false;\r\n+        loadingMessage.value = '';\r\n+      }\r\n     };\r\n \r\n-    const installPlugin = (event: Event) => {\r\n+    const installPlugin = async (event: Event) => {\r\n       const file = (event.target as HTMLInputElement).files?.[0];\r\n-      if (file) {\r\n-        ipcRenderer.invoke('install-plugin', file.path).then(() => {\r\n-          loadPlugins();\r\n-        });\r\n+      if (!file) return;\r\n+\r\n+      try {\r\n+        loading.value = true;\r\n+        loadingMessage.value = '安装插件中...';\r\n+        clearMessages();\r\n+\r\n+        const result = await ipcRenderer.invoke('install-plugin', file.path);\r\n+\r\n+        if (result.success) {\r\n+          successMessage.value = result.message;\r\n+          await loadPlugins();\r\n+        }\r\n+      } catch (err) {\r\n+        const errorMessage = err instanceof Error ? err.message : '插件安装失败';\r\n+        error.value = errorMessage;\r\n+        console.error('Plugin installation failed:', err);\r\n+      } finally {\r\n+        loading.value = false;\r\n+        loadingMessage.value = '';\r\n+        // 清空文件输入\r\n+        (event.target as HTMLInputElement).value = '';\r\n       }\r\n     };\r\n \r\n-    const uninstallPlugin = (pluginName: string) => {\r\n-      ipcRenderer.invoke('uninstall-plugin', pluginName).then(() => {\r\n-        loadPlugins();\r\n-      });\r\n+    const uninstallPlugin = async (pluginName: string) => {\r\n+      if (!confirm(`确定要卸载插件 \"${pluginName}\" 吗？`)) {\r\n+        return;\r\n+      }\r\n+\r\n+      try {\r\n+        loading.value = true;\r\n+        loadingMessage.value = '卸载插件中...';\r\n+        clearMessages();\r\n+\r\n+        const result = await ipcRenderer.invoke('uninstall-plugin', pluginName);\r\n+\r\n+        if (result.success) {\r\n+          successMessage.value = result.message;\r\n+          await loadPlugins();\r\n+        }\r\n+      } catch (err) {\r\n+        const errorMessage = err instanceof Error ? err.message : '插件卸载失败';\r\n+        error.value = errorMessage;\r\n+        console.error('Plugin uninstallation failed:', err);\r\n+      } finally {\r\n+        loading.value = false;\r\n+        loadingMessage.value = '';\r\n+      }\r\n     };\r\n \r\n+    const clearMessages = () => {\r\n+      error.value = '';\r\n+      successMessage.value = '';\r\n+    };\r\n+\r\n+    const clearError = () => {\r\n+      error.value = '';\r\n+    };\r\n+\r\n+    const clearSuccess = () => {\r\n+      successMessage.value = '';\r\n+    };\r\n+\r\n+    const getStatusText = (status: string) => {\r\n+      const statusMap: Record<string, string> = {\r\n+        loading: '加载中',\r\n+        loaded: '已加载',\r\n+        failed: '加载失败',\r\n+        unknown: '未知状态'\r\n+      };\r\n+      return statusMap[status] || status;\r\n+    };\r\n+\r\n     return {\r\n       plugins,\r\n+      loading,\r\n+      loadingMessage,\r\n+      error,\r\n+      successMessage,\r\n       installPlugin,\r\n       uninstallPlugin,\r\n+      clearError,\r\n+      clearSuccess,\r\n+      getStatusText,\r\n     };\r\n   },\r\n });\r\n-\r\n-const ipcRenderer = (window as any).ipcRenderer;\r\n </script>\r\n \r\n <style scoped>\r\n .plugin-manager {\r\n"}, {"date": 1750234932694, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -205,6 +205,219 @@\n \r\n <style scoped>\r\n .plugin-manager {\r\n   padding: 20px;\r\n+  max-width: 1200px;\r\n+  margin: 0 auto;\r\n }\r\n+\r\n+/* 消息提示样式 */\r\n+.error-message, .success-message {\r\n+  padding: 12px 16px;\r\n+  margin-bottom: 16px;\r\n+  border-radius: 6px;\r\n+  display: flex;\r\n+  justify-content: space-between;\r\n+  align-items: center;\r\n+}\r\n+\r\n+.error-message {\r\n+  background-color: #fee;\r\n+  border: 1px solid #fcc;\r\n+  color: #c33;\r\n+}\r\n+\r\n+.success-message {\r\n+  background-color: #efe;\r\n+  border: 1px solid #cfc;\r\n+  color: #363;\r\n+}\r\n+\r\n+.close-btn {\r\n+  background: none;\r\n+  border: none;\r\n+  font-size: 18px;\r\n+  cursor: pointer;\r\n+  padding: 0;\r\n+  margin-left: 10px;\r\n+  opacity: 0.7;\r\n+}\r\n+\r\n+.close-btn:hover {\r\n+  opacity: 1;\r\n+}\r\n+\r\n+/* 加载状态样式 */\r\n+.loading-message {\r\n+  padding: 12px 16px;\r\n+  margin-bottom: 16px;\r\n+  background-color: #f0f8ff;\r\n+  border: 1px solid #b3d9ff;\r\n+  border-radius: 6px;\r\n+  color: #0066cc;\r\n+  display: flex;\r\n+  align-items: center;\r\n+}\r\n+\r\n+.spinner {\r\n+  display: inline-block;\r\n+  width: 16px;\r\n+  height: 16px;\r\n+  border: 2px solid #b3d9ff;\r\n+  border-top: 2px solid #0066cc;\r\n+  border-radius: 50%;\r\n+  animation: spin 1s linear infinite;\r\n+  margin-right: 8px;\r\n+}\r\n+\r\n+@keyframes spin {\r\n+  0% { transform: rotate(0deg); }\r\n+  100% { transform: rotate(360deg); }\r\n+}\r\n+\r\n+/* 安装区域样式 */\r\n+.install-section {\r\n+  margin-bottom: 32px;\r\n+  padding: 20px;\r\n+  background-color: #f9f9f9;\r\n+  border-radius: 8px;\r\n+  border: 1px solid #e0e0e0;\r\n+}\r\n+\r\n+.install-section h2 {\r\n+  margin-top: 0;\r\n+  margin-bottom: 16px;\r\n+  color: #333;\r\n+}\r\n+\r\n+.install-section input[type=\"file\"] {\r\n+  padding: 8px;\r\n+  border: 1px solid #ccc;\r\n+  border-radius: 4px;\r\n+  background-color: white;\r\n+}\r\n+\r\n+.install-section input[type=\"file\"]:disabled {\r\n+  opacity: 0.6;\r\n+  cursor: not-allowed;\r\n+}\r\n+\r\n+/* 插件列表样式 */\r\n+.plugins-section h2 {\r\n+  margin-bottom: 16px;\r\n+  color: #333;\r\n+}\r\n+\r\n+.no-plugins {\r\n+  text-align: center;\r\n+  padding: 40px;\r\n+  color: #666;\r\n+  font-style: italic;\r\n+}\r\n+\r\n+.plugins-grid {\r\n+  display: grid;\r\n+  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n+  gap: 16px;\r\n+}\r\n+\r\n+.plugin-card {\r\n+  background: white;\r\n+  border: 1px solid #e0e0e0;\r\n+  border-radius: 8px;\r\n+  padding: 16px;\r\n+  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n+  transition: box-shadow 0.2s;\r\n+}\r\n+\r\n+.plugin-card:hover {\r\n+  box-shadow: 0 4px 8px rgba(0,0,0,0.15);\r\n+}\r\n+\r\n+.plugin-header {\r\n+  display: flex;\r\n+  justify-content: space-between;\r\n+  align-items: center;\r\n+  margin-bottom: 8px;\r\n+}\r\n+\r\n+.plugin-header h3 {\r\n+  margin: 0;\r\n+  color: #333;\r\n+  font-size: 18px;\r\n+}\r\n+\r\n+.status-badge {\r\n+  padding: 4px 8px;\r\n+  border-radius: 12px;\r\n+  font-size: 12px;\r\n+  font-weight: bold;\r\n+  text-transform: uppercase;\r\n+}\r\n+\r\n+.status-badge.loaded {\r\n+  background-color: #d4edda;\r\n+  color: #155724;\r\n+}\r\n+\r\n+.status-badge.loading {\r\n+  background-color: #d1ecf1;\r\n+  color: #0c5460;\r\n+}\r\n+\r\n+.status-badge.failed {\r\n+  background-color: #f8d7da;\r\n+  color: #721c24;\r\n+}\r\n+\r\n+.status-badge.unknown {\r\n+  background-color: #e2e3e5;\r\n+  color: #383d41;\r\n+}\r\n+\r\n+.plugin-description {\r\n+  color: #666;\r\n+  margin-bottom: 8px;\r\n+  line-height: 1.4;\r\n+}\r\n+\r\n+.plugin-version {\r\n+  font-size: 12px;\r\n+  color: #888;\r\n+  margin-bottom: 8px;\r\n+}\r\n+\r\n+.plugin-error {\r\n+  background-color: #f8d7da;\r\n+  color: #721c24;\r\n+  padding: 8px;\r\n+  border-radius: 4px;\r\n+  font-size: 12px;\r\n+  margin-bottom: 12px;\r\n+  border: 1px solid #f5c6cb;\r\n+}\r\n+\r\n+.plugin-actions {\r\n+  display: flex;\r\n+  gap: 8px;\r\n+}\r\n+\r\n+.uninstall-btn {\r\n+  background-color: #dc3545;\r\n+  color: white;\r\n+  border: none;\r\n+  padding: 6px 12px;\r\n+  border-radius: 4px;\r\n+  cursor: pointer;\r\n+  font-size: 12px;\r\n+  transition: background-color 0.2s;\r\n+}\r\n+\r\n+.uninstall-btn:hover:not(:disabled) {\r\n+  background-color: #c82333;\r\n+}\r\n+\r\n+.uninstall-btn:disabled {\r\n+  opacity: 0.6;\r\n+  cursor: not-allowed;\r\n+}\r\n </style>\r\n"}, {"date": 1750235186619, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -67,18 +67,10 @@\n </template>\r\n \r\n <script lang=\"ts\">\r\n import { defineComponent, ref, onMounted } from 'vue';\r\n+import type { PluginInfo, PluginOperationResult } from '../types/plugin';\r\n \r\n-// 为插件定义类型\r\n-interface Plugin {\r\n-  name: string;\r\n-  description: string;\r\n-  version?: string;\r\n-  status: 'loading' | 'loaded' | 'failed' | 'unknown';\r\n-  error?: string;\r\n-}\r\n-\r\n const ipcRenderer = (window as any).ipcRenderer;\r\n \r\n export default defineComponent({\r\n   name: 'PluginManager',\r\n"}, {"date": 1750235196512, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -74,9 +74,9 @@\n \r\n export default defineComponent({\r\n   name: 'PluginManager',\r\n   setup() {\r\n-    const plugins = ref<Plugin[]>([]);\r\n+    const plugins = ref<PluginInfo[]>([]);\r\n     const loading = ref(false);\r\n     const loadingMessage = ref('');\r\n     const error = ref('');\r\n     const successMessage = ref('');\r\n"}, {"date": 1750235207671, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -90,9 +90,9 @@\n         loading.value = true;\r\n         loadingMessage.value = '加载插件列表...';\r\n         clearMessages();\r\n \r\n-        const loadedPlugins: Plugin[] = await ipcRenderer.invoke('get-plugins');\r\n+        const loadedPlugins: PluginInfo[] = await ipcRenderer.invoke('get-plugins');\r\n         plugins.value = loadedPlugins;\r\n       } catch (err) {\r\n         const errorMessage = err instanceof Error ? err.message : '加载插件列表失败';\r\n         error.value = errorMessage;\r\n"}, {"date": 1750235506599, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -67,9 +67,9 @@\n </template>\r\n \r\n <script lang=\"ts\">\r\n import { defineComponent, ref, onMounted } from 'vue';\r\n-import type { PluginInfo, PluginOperationResult } from '../types/plugin';\r\n+import type { PluginInfo } from '../types/plugin';\r\n \r\n const ipcRenderer = (window as any).ipcRenderer;\r\n \r\n export default defineComponent({\r\n"}], "date": 1750234829908, "name": "Commit-0", "content": "<template>\r\n  <div class=\"plugin-manager\">\r\n    <h1>插件管理</h1>\r\n\r\n    <!-- 错误提示 -->\r\n    <div v-if=\"error\" class=\"error-message\">\r\n      <strong>错误:</strong> {{ error }}\r\n      <button @click=\"clearError\" class=\"close-btn\">×</button>\r\n    </div>\r\n\r\n    <!-- 成功提示 -->\r\n    <div v-if=\"successMessage\" class=\"success-message\">\r\n      <strong>成功:</strong> {{ successMessage }}\r\n      <button @click=\"clearSuccess\" class=\"close-btn\">×</button>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading-message\">\r\n      <span class=\"spinner\"></span> {{ loadingMessage }}\r\n    </div>\r\n\r\n    <!-- 插件安装 -->\r\n    <div class=\"install-section\">\r\n      <h2>安装插件</h2>\r\n      <input\r\n        type=\"file\"\r\n        @change=\"installPlugin\"\r\n        :disabled=\"loading\"\r\n        accept=\".js,.zip\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 插件列表 -->\r\n    <div class=\"plugins-section\">\r\n      <h2>已安装插件 ({{ plugins.length }})</h2>\r\n      <div v-if=\"plugins.length === 0\" class=\"no-plugins\">\r\n        暂无已安装的插件\r\n      </div>\r\n      <div v-else class=\"plugins-grid\">\r\n        <div v-for=\"plugin in plugins\" :key=\"plugin.name\" class=\"plugin-card\">\r\n          <div class=\"plugin-header\">\r\n            <h3>{{ plugin.name }}</h3>\r\n            <span :class=\"['status-badge', plugin.status]\">\r\n              {{ getStatusText(plugin.status) }}\r\n            </span>\r\n          </div>\r\n          <p class=\"plugin-description\">{{ plugin.description }}</p>\r\n          <div v-if=\"plugin.version\" class=\"plugin-version\">\r\n            版本: {{ plugin.version }}\r\n          </div>\r\n          <div v-if=\"plugin.error\" class=\"plugin-error\">\r\n            错误: {{ plugin.error }}\r\n          </div>\r\n          <div class=\"plugin-actions\">\r\n            <button\r\n              @click=\"uninstallPlugin(plugin.name)\"\r\n              :disabled=\"loading\"\r\n              class=\"uninstall-btn\"\r\n            >\r\n              卸载\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { defineComponent, ref, onMounted } from 'vue';\r\n\r\n// 为插件定义类型\r\ninterface Plugin {\r\n  name: string;\r\n  description: string;\r\n}\r\n\r\nexport default defineComponent({\r\n  name: 'PluginManager',\r\n  setup() {\r\n    // 修改 plugins 的类型\r\n    const plugins = ref<Plugin[]>([]);\r\n\r\n    onMounted(() => {\r\n      loadPlugins();\r\n    });\r\n\r\n    const loadPlugins = () => {\r\n      // 为 loadedPlugins 显式指定类型\r\n      ipcRenderer.invoke('get-plugins').then((loadedPlugins: Plugin[]) => {\r\n        plugins.value = loadedPlugins;\r\n      });\r\n    };\r\n\r\n    const installPlugin = (event: Event) => {\r\n      const file = (event.target as HTMLInputElement).files?.[0];\r\n      if (file) {\r\n        ipcRenderer.invoke('install-plugin', file.path).then(() => {\r\n          loadPlugins();\r\n        });\r\n      }\r\n    };\r\n\r\n    const uninstallPlugin = (pluginName: string) => {\r\n      ipcRenderer.invoke('uninstall-plugin', pluginName).then(() => {\r\n        loadPlugins();\r\n      });\r\n    };\r\n\r\n    return {\r\n      plugins,\r\n      installPlugin,\r\n      uninstallPlugin,\r\n    };\r\n  },\r\n});\r\n\r\nconst ipcRenderer = (window as any).ipcRenderer;\r\n</script>\r\n\r\n<style scoped>\r\n.plugin-manager {\r\n  padding: 20px;\r\n}\r\n</style>\r\n"}]}