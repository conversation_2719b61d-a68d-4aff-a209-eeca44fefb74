{"sourceFile": "terminal-app --template vue-ts/development-plan.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750232704269, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750232704269, "name": "Commit-0", "content": "# 开发计划\r\n\r\n## 项目目标\r\n开发一款基于 Electron 的终端软件，支持 SSH、SFTP、SerialPort 等功能，并以插件形式扩展功能。\r\n\r\n## 实现步骤\r\n\r\n### 1. 初始化项目\r\n- 使用 `pnpm` 和 `electron-vite` 初始化项目。\r\n- 配置 TypeScript 和 Vue。\r\n\r\n### 2. 实现主程序\r\n- 搭建 Electron 主进程和渲染进程的通信机制。\r\n- 提供插件加载和管理的基础能力。\r\n\r\n### 3. 开发插件系统\r\n- 定义插件的元数据格式（`manifest.json`）。\r\n- 实现插件的动态加载和卸载功能。\r\n- 创建示例插件，包含激活和停用逻辑。\r\n\r\n### 4. 设计用户界面\r\n- 使用 Vue 构建主界面。\r\n- 添加插件管理页面，展示已加载插件列表。\r\n- 提供插件安装和卸载功能。\r\n\r\n### 5. 修复问题\r\n- 修复 Electron 安装失败的问题：\r\n  - 删除 `node_modules` 和 Electron 缓存，重新安装依赖。\r\n- 修复渲染进程中动态导入 `electron` 的问题：\r\n  - 使用 `preload.mjs` 和 `contextBridge` 暴露 `ipcRenderer`。\r\n  - 修改渲染进程代码，通过 `window.ipcRenderer` 访问 Electron 功能。\r\n\r\n## 当前状态\r\n- 项目初始化完成。\r\n- 主程序支持插件加载。\r\n- 插件管理页面已实现，支持插件的安装和卸载。\r\n- 修复了 Electron 安装和动态导入问题。\r\n\r\n## 下一步计划\r\n- 优化插件系统，支持更多功能扩展。\r\n- 增加插件市场，支持在线浏览和安装插件。\r\n- 提高界面美观性和用户体验。\r\n\r\n---\r\n记录日期：2025年6月18日\r\n"}]}