{"sourceFile": "src/App.vue", "activeCommit": 0, "commits": [{"activePatchIndex": 13, "patches": [{"date": 1750234157947, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750234173435, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,11 @@\n <script setup lang=\"ts\">\r\n import PluginManager from './views/PluginManager.vue';\r\n-\r\n+import { ipcRenderer } from 'electron';\r\n function execFeature(feature: string) {\r\n   ipcRenderer.invoke('get-plugin-feature', 'my-plugin', 'feature').then(feature => {\r\n-  feature(); // 调用插件提供的功能\r\n-});\r\n+    feature(); // 调用插件提供的功能\r\n+  });\r\n }\r\n </script>\r\n \r\n <template>\r\n@@ -18,11 +18,13 @@\n   padding: 1.5em;\r\n   will-change: filter;\r\n   transition: filter 300ms;\r\n }\r\n+\r\n .logo:hover {\r\n   filter: drop-shadow(0 0 2em #646cffaa);\r\n }\r\n+\r\n .logo.vue:hover {\r\n   filter: drop-shadow(0 0 2em #42b883aa);\r\n }\r\n </style>\r\n"}, {"date": 1750234196891, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,8 +8,9 @@\n }\r\n </script>\r\n \r\n <template>\r\n+  <button @click=\"execFeature('feature')\">执行</button>\r\n   <PluginManager />\r\n </template>\r\n \r\n <style scoped>\r\n"}, {"date": 1750234222402, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,9 +8,9 @@\n }\r\n </script>\r\n \r\n <template>\r\n-  <button @click=\"execFeature('feature')\">执行</button>\r\n+  <button @click=\"execFeature('Example')\">执行</button>\r\n   <PluginManager />\r\n </template>\r\n \r\n <style scoped>\r\n"}, {"date": 1750234227842, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,9 @@\n <script setup lang=\"ts\">\r\n import PluginManager from './views/PluginManager.vue';\r\n import { ipcRenderer } from 'electron';\r\n function execFeature(feature: string) {\r\n-  ipcRenderer.invoke('get-plugin-feature', 'my-plugin', 'feature').then(feature => {\r\n+  ipcRenderer.invoke('get-plugin-feature', feature, 'feature').then(feature => {\r\n     feature(); // 调用插件提供的功能\r\n   });\r\n }\r\n </script>\r\n"}, {"date": 1750234282334, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,9 @@\n <script setup lang=\"ts\">\r\n import PluginManager from './views/PluginManager.vue';\r\n import { ipcRenderer } from 'electron';\r\n function execFeature(feature: string) {\r\n-  ipcRenderer.invoke('get-plugin-feature', feature, 'feature').then(feature => {\r\n+  window.ipcRenderer.invoke('get-plugin-feature', feature, 'feature').then(feature => {\r\n     feature(); // 调用插件提供的功能\r\n   });\r\n }\r\n </script>\r\n"}, {"date": 1750234453944, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,8 @@\n <script setup lang=\"ts\">\r\n import PluginManager from './views/PluginManager.vue';\r\n-import { ipcRenderer } from 'electron';\r\n function execFeature(feature: string) {\r\n-  window.ipcRenderer.invoke('get-plugin-feature', feature, 'feature').then(feature => {\r\n+  window.ipcRenderer.invoke('get-plugin-feature', feature, 'activate').then(feature => {\r\n     feature(); // 调用插件提供的功能\r\n   });\r\n }\r\n </script>\r\n"}, {"date": 1750234954641, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,49 @@\n <script setup lang=\"ts\">\r\n+import { ref } from 'vue';\r\n import PluginManager from './views/PluginManager.vue';\r\n-function execFeature(feature: string) {\r\n-  window.ipcRenderer.invoke('get-plugin-feature', feature, 'activate').then(feature => {\r\n-    feature(); // 调用插件提供的功能\r\n-  });\r\n+\r\n+const executing = ref(false);\r\n+const executionMessage = ref('');\r\n+const executionError = ref('');\r\n+\r\n+async function execFeature(pluginName: string, featureName: string = 'activate') {\r\n+  try {\r\n+    executing.value = true;\r\n+    executionMessage.value = `执行插件 ${pluginName} 的 ${featureName} 功能...`;\r\n+    executionError.value = '';\r\n+\r\n+    const feature = await window.ipcRenderer.invoke('get-plugin-feature', pluginName, featureName);\r\n+\r\n+    if (typeof feature === 'function') {\r\n+      const result = await feature();\r\n+      executionMessage.value = `插件 ${pluginName} 执行成功`;\r\n+\r\n+      // 如果有返回结果，显示给用户\r\n+      if (result !== undefined) {\r\n+        console.log('Plugin execution result:', result);\r\n+      }\r\n+    } else {\r\n+      throw new Error('获取的功能不是一个函数');\r\n+    }\r\n+  } catch (error) {\r\n+    const errorMessage = error instanceof Error ? error.message : '执行插件功能时发生未知错误';\r\n+    executionError.value = `执行失败: ${errorMessage}`;\r\n+    console.error('Plugin execution failed:', error);\r\n+  } finally {\r\n+    executing.value = false;\r\n+    // 3秒后清除消息\r\n+    setTimeout(() => {\r\n+      executionMessage.value = '';\r\n+      executionError.value = '';\r\n+    }, 3000);\r\n+  }\r\n }\r\n+\r\n+function clearMessages() {\r\n+  executionMessage.value = '';\r\n+  executionError.value = '';\r\n+}\r\n </script>\r\n \r\n <template>\r\n   <button @click=\"execFeature('Example')\">执行</button>\r\n"}, {"date": 1750234970107, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -45,10 +45,42 @@\n }\r\n </script>\r\n \r\n <template>\r\n-  <button @click=\"execFeature('Example')\">执行</button>\r\n-  <PluginManager />\r\n+  <div class=\"app\">\r\n+    <header class=\"app-header\">\r\n+      <h1>插件系统演示</h1>\r\n+    </header>\r\n+\r\n+    <main class=\"app-main\">\r\n+      <!-- 执行状态显示 -->\r\n+      <div v-if=\"executionMessage\" class=\"execution-message success\">\r\n+        {{ executionMessage }}\r\n+      </div>\r\n+\r\n+      <div v-if=\"executionError\" class=\"execution-message error\">\r\n+        {{ executionError }}\r\n+        <button @click=\"clearMessages\" class=\"close-btn\">×</button>\r\n+      </div>\r\n+\r\n+      <!-- 快速执行区域 -->\r\n+      <section class=\"quick-actions\">\r\n+        <h2>快速执行</h2>\r\n+        <div class=\"action-buttons\">\r\n+          <button\r\n+            @click=\"execFeature('Example', 'activate')\"\r\n+            :disabled=\"executing\"\r\n+            class=\"action-btn\"\r\n+          >\r\n+            {{ executing ? '执行中...' : '执行示例插件' }}\r\n+          </button>\r\n+        </div>\r\n+      </section>\r\n+\r\n+      <!-- 插件管理器 -->\r\n+      <PluginManager />\r\n+    </main>\r\n+  </div>\r\n </template>\r\n \r\n <style scoped>\r\n .logo {\r\n"}, {"date": 1750234993486, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -82,19 +82,116 @@\n   </div>\r\n </template>\r\n \r\n <style scoped>\r\n-.logo {\r\n-  height: 6em;\r\n-  padding: 1.5em;\r\n-  will-change: filter;\r\n-  transition: filter 300ms;\r\n+.app {\r\n+  min-height: 100vh;\r\n+  background-color: #f5f5f5;\r\n }\r\n \r\n-.logo:hover {\r\n-  filter: drop-shadow(0 0 2em #646cffaa);\r\n+.app-header {\r\n+  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n+  color: white;\r\n+  padding: 20px 0;\r\n+  text-align: center;\r\n+  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\r\n }\r\n \r\n-.logo.vue:hover {\r\n-  filter: drop-shadow(0 0 2em #42b883aa);\r\n+.app-header h1 {\r\n+  margin: 0;\r\n+  font-size: 2.5em;\r\n+  font-weight: 300;\r\n }\r\n+\r\n+.app-main {\r\n+  max-width: 1200px;\r\n+  margin: 0 auto;\r\n+  padding: 20px;\r\n+}\r\n+\r\n+/* 执行状态消息 */\r\n+.execution-message {\r\n+  padding: 12px 16px;\r\n+  margin-bottom: 20px;\r\n+  border-radius: 6px;\r\n+  display: flex;\r\n+  justify-content: space-between;\r\n+  align-items: center;\r\n+  font-weight: 500;\r\n+}\r\n+\r\n+.execution-message.success {\r\n+  background-color: #d4edda;\r\n+  border: 1px solid #c3e6cb;\r\n+  color: #155724;\r\n+}\r\n+\r\n+.execution-message.error {\r\n+  background-color: #f8d7da;\r\n+  border: 1px solid #f5c6cb;\r\n+  color: #721c24;\r\n+}\r\n+\r\n+.close-btn {\r\n+  background: none;\r\n+  border: none;\r\n+  font-size: 18px;\r\n+  cursor: pointer;\r\n+  padding: 0;\r\n+  margin-left: 10px;\r\n+  opacity: 0.7;\r\n+}\r\n+\r\n+.close-btn:hover {\r\n+  opacity: 1;\r\n+}\r\n+\r\n+/* 快速执行区域 */\r\n+.quick-actions {\r\n+  background: white;\r\n+  padding: 24px;\r\n+  border-radius: 8px;\r\n+  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n+  margin-bottom: 32px;\r\n+}\r\n+\r\n+.quick-actions h2 {\r\n+  margin-top: 0;\r\n+  margin-bottom: 16px;\r\n+  color: #333;\r\n+  font-size: 1.5em;\r\n+}\r\n+\r\n+.action-buttons {\r\n+  display: flex;\r\n+  gap: 12px;\r\n+  flex-wrap: wrap;\r\n+}\r\n+\r\n+.action-btn {\r\n+  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n+  color: white;\r\n+  border: none;\r\n+  padding: 12px 24px;\r\n+  border-radius: 6px;\r\n+  cursor: pointer;\r\n+  font-size: 14px;\r\n+  font-weight: 500;\r\n+  transition: all 0.3s ease;\r\n+  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n+}\r\n+\r\n+.action-btn:hover:not(:disabled) {\r\n+  transform: translateY(-2px);\r\n+  box-shadow: 0 4px 12px rgba(0,0,0,0.2);\r\n+}\r\n+\r\n+.action-btn:disabled {\r\n+  opacity: 0.6;\r\n+  cursor: not-allowed;\r\n+  transform: none;\r\n+}\r\n+\r\n+.action-btn:active:not(:disabled) {\r\n+  transform: translateY(0);\r\n+}\r\n </style>\r\n"}, {"date": 1750235692076, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -66,9 +66,9 @@\n       <section class=\"quick-actions\">\r\n         <h2>快速执行</h2>\r\n         <div class=\"action-buttons\">\r\n           <button\r\n-            @click=\"execFeature('Example', 'activate')\"\r\n+            @click=\"execFeature('Example', 'feature')\"\r\n             :disabled=\"executing\"\r\n             class=\"action-btn\"\r\n           >\r\n             {{ executing ? '执行中...' : '执行示例插件' }}\r\n"}, {"date": 1750235715607, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,9 +5,9 @@\n const executing = ref(false);\r\n const executionMessage = ref('');\r\n const executionError = ref('');\r\n \r\n-async function execFeature(pluginName: string, featureName: string = 'activate') {\r\n+async function execFeature(pluginName: string, featureName: string = 'feature') {\r\n   try {\r\n     executing.value = true;\r\n     executionMessage.value = `执行插件 ${pluginName} 的 ${featureName} 功能...`;\r\n     executionError.value = '';\r\n"}, {"date": 1750235897445, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -70,10 +70,58 @@\n             @click=\"execFeature('Example', 'feature')\"\r\n             :disabled=\"executing\"\r\n             class=\"action-btn\"\r\n           >\r\n-            {{ executing ? '执行中...' : '执行示例插件' }}\r\n+            {{ executing ? '执行中...' : '执行基础示例' }}\r\n           </button>\r\n+\r\n+          <button\r\n+            @click=\"execFeature('AdvancedExample', 'showSystemInfo')\"\r\n+            :disabled=\"executing\"\r\n+            class=\"action-btn\"\r\n+          >\r\n+            {{ executing ? '执行中...' : '显示系统信息' }}\r\n+          </button>\r\n+\r\n+          <button\r\n+            @click=\"execFeature('AdvancedExample', 'createTestFile')\"\r\n+            :disabled=\"executing\"\r\n+            class=\"action-btn\"\r\n+          >\r\n+            {{ executing ? '执行中...' : '创建测试文件' }}\r\n+          </button>\r\n+\r\n+          <button\r\n+            @click=\"execFeature('AdvancedExample', 'openFileDialog')\"\r\n+            :disabled=\"executing\"\r\n+            class=\"action-btn\"\r\n+          >\r\n+            {{ executing ? '执行中...' : '打开文件对话框' }}\r\n+          </button>\r\n+\r\n+          <button\r\n+            @click=\"execFeature('AdvancedExample', 'storageDemo')\"\r\n+            :disabled=\"executing\"\r\n+            class=\"action-btn\"\r\n+          >\r\n+            {{ executing ? '执行中...' : '存储演示' }}\r\n+          </button>\r\n+\r\n+          <button\r\n+            @click=\"execFeature('AdvancedExample', 'eventDemo')\"\r\n+            :disabled=\"executing\"\r\n+            class=\"action-btn\"\r\n+          >\r\n+            {{ executing ? '执行中...' : '事件演示' }}\r\n+          </button>\r\n+\r\n+          <button\r\n+            @click=\"execFeature('AdvancedExample', 'createWindow')\"\r\n+            :disabled=\"executing\"\r\n+            class=\"action-btn\"\r\n+          >\r\n+            {{ executing ? '执行中...' : '创建新窗口' }}\r\n+          </button>\r\n         </div>\r\n       </section>\r\n \r\n       <!-- 插件管理器 -->\r\n"}, {"date": 1750235971455, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,26 +5,25 @@\n const executing = ref(false);\r\n const executionMessage = ref('');\r\n const executionError = ref('');\r\n \r\n-async function execFeature(pluginName: string, featureName: string = 'feature') {\r\n+async function execFeature(pluginName: string, featureName: string = 'feature', ...args: any[]) {\r\n   try {\r\n     executing.value = true;\r\n     executionMessage.value = `执行插件 ${pluginName} 的 ${featureName} 功能...`;\r\n     executionError.value = '';\r\n \r\n-    const feature = await window.ipcRenderer.invoke('get-plugin-feature', pluginName, featureName);\r\n+    const response = await window.ipcRenderer.invoke('execute-plugin-feature', pluginName, featureName, ...args);\r\n \r\n-    if (typeof feature === 'function') {\r\n-      const result = await feature();\r\n+    if (response.success) {\r\n       executionMessage.value = `插件 ${pluginName} 执行成功`;\r\n \r\n       // 如果有返回结果，显示给用户\r\n-      if (result !== undefined) {\r\n-        console.log('Plugin execution result:', result);\r\n+      if (response.result !== undefined) {\r\n+        console.log('Plugin execution result:', response.result);\r\n       }\r\n     } else {\r\n-      throw new Error('获取的功能不是一个函数');\r\n+      throw new Error(response.error || '插件执行失败');\r\n     }\r\n   } catch (error) {\r\n     const errorMessage = error instanceof Error ? error.message : '执行插件功能时发生未知错误';\r\n     executionError.value = `执行失败: ${errorMessage}`;\r\n"}], "date": 1750234157947, "name": "Commit-0", "content": "<script setup lang=\"ts\">\r\nimport PluginManager from './views/PluginManager.vue';\r\n\r\nfunction execFeature(feature: string) {\r\n  ipcRenderer.invoke('get-plugin-feature', 'my-plugin', 'feature').then(feature => {\r\n  feature(); // 调用插件提供的功能\r\n});\r\n}\r\n</script>\r\n\r\n<template>\r\n  <PluginManager />\r\n</template>\r\n\r\n<style scoped>\r\n.logo {\r\n  height: 6em;\r\n  padding: 1.5em;\r\n  will-change: filter;\r\n  transition: filter 300ms;\r\n}\r\n.logo:hover {\r\n  filter: drop-shadow(0 0 2em #646cffaa);\r\n}\r\n.logo.vue:hover {\r\n  filter: drop-shadow(0 0 2em #42b883aa);\r\n}\r\n</style>\r\n"}]}