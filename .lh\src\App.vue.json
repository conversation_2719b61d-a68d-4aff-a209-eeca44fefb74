{"sourceFile": "src/App.vue", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1750234157947, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750234173435, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,11 @@\n <script setup lang=\"ts\">\r\n import PluginManager from './views/PluginManager.vue';\r\n-\r\n+import { ipcRenderer } from 'electron';\r\n function execFeature(feature: string) {\r\n   ipcRenderer.invoke('get-plugin-feature', 'my-plugin', 'feature').then(feature => {\r\n-  feature(); // 调用插件提供的功能\r\n-});\r\n+    feature(); // 调用插件提供的功能\r\n+  });\r\n }\r\n </script>\r\n \r\n <template>\r\n@@ -18,11 +18,13 @@\n   padding: 1.5em;\r\n   will-change: filter;\r\n   transition: filter 300ms;\r\n }\r\n+\r\n .logo:hover {\r\n   filter: drop-shadow(0 0 2em #646cffaa);\r\n }\r\n+\r\n .logo.vue:hover {\r\n   filter: drop-shadow(0 0 2em #42b883aa);\r\n }\r\n </style>\r\n"}, {"date": 1750234196891, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,8 +8,9 @@\n }\r\n </script>\r\n \r\n <template>\r\n+  <button @click=\"execFeature('feature')\">执行</button>\r\n   <PluginManager />\r\n </template>\r\n \r\n <style scoped>\r\n"}, {"date": 1750234222402, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,9 +8,9 @@\n }\r\n </script>\r\n \r\n <template>\r\n-  <button @click=\"execFeature('feature')\">执行</button>\r\n+  <button @click=\"execFeature('Example')\">执行</button>\r\n   <PluginManager />\r\n </template>\r\n \r\n <style scoped>\r\n"}, {"date": 1750234227842, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,9 @@\n <script setup lang=\"ts\">\r\n import PluginManager from './views/PluginManager.vue';\r\n import { ipcRenderer } from 'electron';\r\n function execFeature(feature: string) {\r\n-  ipcRenderer.invoke('get-plugin-feature', 'my-plugin', 'feature').then(feature => {\r\n+  ipcRenderer.invoke('get-plugin-feature', feature, 'feature').then(feature => {\r\n     feature(); // 调用插件提供的功能\r\n   });\r\n }\r\n </script>\r\n"}, {"date": 1750234282334, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,9 @@\n <script setup lang=\"ts\">\r\n import PluginManager from './views/PluginManager.vue';\r\n import { ipcRenderer } from 'electron';\r\n function execFeature(feature: string) {\r\n-  ipcRenderer.invoke('get-plugin-feature', feature, 'feature').then(feature => {\r\n+  window.ipcRenderer.invoke('get-plugin-feature', feature, 'feature').then(feature => {\r\n     feature(); // 调用插件提供的功能\r\n   });\r\n }\r\n </script>\r\n"}, {"date": 1750234453944, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,8 @@\n <script setup lang=\"ts\">\r\n import PluginManager from './views/PluginManager.vue';\r\n-import { ipcRenderer } from 'electron';\r\n function execFeature(feature: string) {\r\n-  window.ipcRenderer.invoke('get-plugin-feature', feature, 'feature').then(feature => {\r\n+  window.ipcRenderer.invoke('get-plugin-feature', feature, 'activate').then(feature => {\r\n     feature(); // 调用插件提供的功能\r\n   });\r\n }\r\n </script>\r\n"}], "date": 1750234157947, "name": "Commit-0", "content": "<script setup lang=\"ts\">\r\nimport PluginManager from './views/PluginManager.vue';\r\n\r\nfunction execFeature(feature: string) {\r\n  ipcRenderer.invoke('get-plugin-feature', 'my-plugin', 'feature').then(feature => {\r\n  feature(); // 调用插件提供的功能\r\n});\r\n}\r\n</script>\r\n\r\n<template>\r\n  <PluginManager />\r\n</template>\r\n\r\n<style scoped>\r\n.logo {\r\n  height: 6em;\r\n  padding: 1.5em;\r\n  will-change: filter;\r\n  transition: filter 300ms;\r\n}\r\n.logo:hover {\r\n  filter: drop-shadow(0 0 2em #646cffaa);\r\n}\r\n.logo.vue:hover {\r\n  filter: drop-shadow(0 0 2em #42b883aa);\r\n}\r\n</style>\r\n"}]}