{"sourceFile": "plugins/example/manifest.json", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1750233017082, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750233022261, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,3 +1,5 @@\n {\r\n-\r\n+  \"name\": \"Example Plugin\",\r\n+  \"description\": \"A simple example plugin\",\r\n+  \"main\": \"index.js\"\r\n }\n\\ No newline at end of file\n"}, {"date": 1750233032173, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n {\r\n   \"name\": \"Example Plugin\",\r\n-  \"description\": \"A simple example plugin\",\r\n+  \"description\": \"这是一个测试插件\",\r\n   \"main\": \"index.js\"\r\n }\n\\ No newline at end of file\n"}], "date": 1750233017082, "name": "Commit-0", "content": "{\r\n\r\n}"}]}