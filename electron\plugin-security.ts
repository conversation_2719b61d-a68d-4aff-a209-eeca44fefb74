/**
 * 插件安全管理模块
 */

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import type { PluginManifest } from '../src/types/plugin';

/**
 * 插件权限枚举
 */
export enum PluginPermission {
  FILE_SYSTEM = 'fs',
  DIALOG = 'dialog',
  NOTIFICATION = 'notification',
  SHELL = 'shell',
  EXEC = 'exec',
  WINDOW = 'window',
  STORAGE = 'storage',
  EVENTS = 'events',
  NETWORK = 'network'
}

/**
 * 安全策略配置
 */
export interface SecurityPolicy {
  /** 是否启用权限检查 */
  enablePermissionCheck: boolean;
  /** 是否启用代码签名验证 */
  enableCodeSigning: boolean;
  /** 允许的最大文件大小（字节） */
  maxFileSize: number;
  /** 允许的文件扩展名 */
  allowedExtensions: string[];
  /** 禁止的模块列表 */
  blockedModules: string[];
  /** 是否启用沙箱模式 */
  enableSandbox: boolean;
}

/**
 * 默认安全策略
 */
export const DEFAULT_SECURITY_POLICY: SecurityPolicy = {
  enablePermissionCheck: true,
  enableCodeSigning: false, // 在生产环境中应该启用
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedExtensions: ['.js', '.mjs'],
  blockedModules: ['child_process', 'fs', 'path', 'os', 'crypto'],
  enableSandbox: false // 在生产环境中应该启用
};

/**
 * 插件安全管理器
 */
export class PluginSecurityManager {
  private policy: SecurityPolicy;
  private trustedPlugins: Set<string> = new Set();

  constructor(policy: SecurityPolicy = DEFAULT_SECURITY_POLICY) {
    this.policy = policy;
  }

  /**
   * 验证插件清单
   */
  validateManifest(manifest: PluginManifest): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查必需字段
    if (!manifest.name || typeof manifest.name !== 'string') {
      errors.push('Plugin name is required and must be a string');
    }

    if (!manifest.main || typeof manifest.main !== 'string') {
      errors.push('Plugin main entry is required and must be a string');
    }

    if (!manifest.description || typeof manifest.description !== 'string') {
      errors.push('Plugin description is required and must be a string');
    }

    // 验证版本格式
    if (manifest.version && !/^\d+\.\d+\.\d+/.test(manifest.version)) {
      errors.push('Plugin version must follow semantic versioning (x.y.z)');
    }

    // 验证权限
    if (manifest.permissions) {
      if (!Array.isArray(manifest.permissions)) {
        errors.push('Plugin permissions must be an array');
      } else {
        const validPermissions = Object.values(PluginPermission);
        const invalidPermissions = manifest.permissions.filter(
          perm => !validPermissions.includes(perm as PluginPermission)
        );
        if (invalidPermissions.length > 0) {
          errors.push(`Invalid permissions: ${invalidPermissions.join(', ')}`);
        }
      }
    }

    // 检查插件名称是否安全
    if (manifest.name && !/^[a-zA-Z0-9_-]+$/.test(manifest.name)) {
      errors.push('Plugin name can only contain letters, numbers, underscores, and hyphens');
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * 验证插件文件
   */
  async validatePluginFile(filePath: string): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        errors.push('Plugin file does not exist');
        return { valid: false, errors };
      }

      // 检查文件大小
      const stats = fs.statSync(filePath);
      if (stats.size > this.policy.maxFileSize) {
        errors.push(`Plugin file size (${stats.size}) exceeds maximum allowed size (${this.policy.maxFileSize})`);
      }

      // 检查文件扩展名
      const ext = path.extname(filePath);
      if (!this.policy.allowedExtensions.includes(ext)) {
        errors.push(`Plugin file extension '${ext}' is not allowed`);
      }

      // 检查文件内容（基本的恶意代码检测）
      const content = fs.readFileSync(filePath, 'utf-8');
      
      // 检查是否包含危险的模块导入
      for (const blockedModule of this.policy.blockedModules) {
        const importRegex = new RegExp(`require\\s*\\(\\s*['"\`]${blockedModule}['"\`]\\s*\\)`, 'g');
        const importRegex2 = new RegExp(`from\\s+['"\`]${blockedModule}['"\`]`, 'g');
        
        if (importRegex.test(content) || importRegex2.test(content)) {
          errors.push(`Plugin contains blocked module import: ${blockedModule}`);
        }
      }

      // 检查是否包含危险的函数调用
      const dangerousFunctions = ['eval', 'Function', 'setTimeout', 'setInterval'];
      for (const func of dangerousFunctions) {
        const funcRegex = new RegExp(`\\b${func}\\s*\\(`, 'g');
        if (funcRegex.test(content)) {
          errors.push(`Plugin contains potentially dangerous function: ${func}`);
        }
      }

    } catch (error) {
      errors.push(`Error validating plugin file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * 检查插件权限
   */
  checkPermission(pluginName: string, permission: PluginPermission, manifest?: PluginManifest): boolean {
    if (!this.policy.enablePermissionCheck) {
      return true;
    }

    // 检查插件是否在信任列表中
    if (this.trustedPlugins.has(pluginName)) {
      return true;
    }

    // 检查清单中的权限
    if (manifest && manifest.permissions) {
      return manifest.permissions.includes(permission);
    }

    return false;
  }

  /**
   * 添加信任的插件
   */
  addTrustedPlugin(pluginName: string): void {
    this.trustedPlugins.add(pluginName);
  }

  /**
   * 移除信任的插件
   */
  removeTrustedPlugin(pluginName: string): void {
    this.trustedPlugins.delete(pluginName);
  }

  /**
   * 生成插件签名
   */
  generatePluginSignature(pluginPath: string): string {
    const content = fs.readFileSync(pluginPath);
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  /**
   * 验证插件签名
   */
  verifyPluginSignature(pluginPath: string, expectedSignature: string): boolean {
    if (!this.policy.enableCodeSigning) {
      return true;
    }

    const actualSignature = this.generatePluginSignature(pluginPath);
    return actualSignature === expectedSignature;
  }

  /**
   * 创建安全的插件API代理
   */
  createSecureAPIProxy(pluginName: string, api: any, manifest?: PluginManifest): any {
    const secureAPI: any = {};

    // 为每个API模块创建权限检查代理
    for (const [moduleName, moduleAPI] of Object.entries(api)) {
      const permission = moduleName as PluginPermission;
      
      if (this.checkPermission(pluginName, permission, manifest)) {
        secureAPI[moduleName] = moduleAPI;
      } else {
        // 创建一个拒绝访问的代理
        secureAPI[moduleName] = new Proxy({}, {
          get: () => {
            throw new Error(`Plugin "${pluginName}" does not have permission to access "${moduleName}"`);
          }
        });
      }
    }

    return secureAPI;
  }

  /**
   * 更新安全策略
   */
  updatePolicy(newPolicy: Partial<SecurityPolicy>): void {
    this.policy = { ...this.policy, ...newPolicy };
  }

  /**
   * 获取当前安全策略
   */
  getPolicy(): SecurityPolicy {
    return { ...this.policy };
  }
}

// 导出单例实例
export const securityManager = new PluginSecurityManager();
