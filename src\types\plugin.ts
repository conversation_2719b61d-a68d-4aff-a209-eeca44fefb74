// 插件系统类型定义

/**
 * 插件状态枚举
 */
export type PluginStatus = 'loading' | 'loaded' | 'failed' | 'unknown';

/**
 * 插件清单文件接口
 */
export interface PluginManifest {
  /** 插件名称 */
  name: string;
  /** 插件描述 */
  description: string;
  /** 插件版本 */
  version?: string;
  /** 插件入口文件 */
  main: string;
  /** 插件作者 */
  author?: string;
  /** 插件许可证 */
  license?: string;
  /** 插件关键词 */
  keywords?: string[];
  /** 插件依赖 */
  dependencies?: Record<string, string>;
  /** 插件权限要求 */
  permissions?: string[];
  /** 最小系统版本要求 */
  minSystemVersion?: string;
}

/**
 * 插件信息接口（用于UI显示）
 */
export interface PluginInfo {
  /** 插件名称 */
  name: string;
  /** 插件描述 */
  description: string;
  /** 插件版本 */
  version?: string;
  /** 插件状态 */
  status: PluginStatus;
  /** 错误信息（如果加载失败） */
  error?: string;
  /** 插件作者 */
  author?: string;
  /** 插件许可证 */
  license?: string;
}

/**
 * 插件功能接口
 */
export interface PluginFeature {
  /** 功能名称 */
  name: string;
  /** 功能描述 */
  description?: string;
  /** 功能函数 */
  handler: (...args: any[]) => any;
}

/**
 * 插件功能集合
 */
export type PluginFeatures = Record<string, (...args: any[]) => any>;

/**
 * 插件模块接口
 */
export interface PluginModule {
  /** 插件激活函数 */
  activate: () => PluginFeatures | Promise<PluginFeatures>;
  /** 插件停用函数（可选） */
  deactivate?: () => void | Promise<void>;
}

/**
 * 插件加载状态
 */
export interface PluginLoadStatus {
  /** 插件名称 */
  name: string;
  /** 加载状态 */
  status: PluginStatus;
  /** 错误信息 */
  error?: string;
  /** 加载时间戳 */
  loadedAt?: number;
}

/**
 * 插件操作结果
 */
export interface PluginOperationResult {
  /** 操作是否成功 */
  success: boolean;
  /** 操作消息 */
  message: string;
  /** 错误详情（如果失败） */
  error?: string;
}

/**
 * IPC 通信接口
 */
export interface PluginIPC {
  /** 获取插件列表 */
  'get-plugins': () => Promise<PluginInfo[]>;
  /** 安装插件 */
  'install-plugin': (pluginPath: string) => Promise<PluginOperationResult>;
  /** 卸载插件 */
  'uninstall-plugin': (pluginName: string) => Promise<PluginOperationResult>;
  /** 获取插件功能 */
  'get-plugin-feature': (pluginName: string, featureName: string) => Promise<(...args: any[]) => any>;
  /** 获取插件状态 */
  'get-plugin-status': () => Promise<PluginLoadStatus[]>;
}

/**
 * 扩展 Window 接口以包含 ipcRenderer
 */
declare global {
  interface Window {
    ipcRenderer: {
      invoke<K extends keyof PluginIPC>(channel: K, ...args: Parameters<PluginIPC[K]>): ReturnType<PluginIPC[K]>;
      on(channel: string, listener: (event: any, ...args: any[]) => void): void;
      off(channel: string, listener?: (...args: any[]) => void): void;
      send(channel: string, ...args: any[]): void;
    };
  }
}

/**
 * 插件系统配置
 */
export interface PluginSystemConfig {
  /** 插件目录路径 */
  pluginsDir: string;
  /** 是否启用沙箱模式 */
  sandboxMode: boolean;
  /** 最大插件数量 */
  maxPlugins?: number;
  /** 插件加载超时时间（毫秒） */
  loadTimeout?: number;
}

/**
 * 插件安全策略
 */
export interface PluginSecurityPolicy {
  /** 允许的文件系统访问路径 */
  allowedPaths?: string[];
  /** 允许的网络访问域名 */
  allowedDomains?: string[];
  /** 禁用的Node.js模块 */
  disallowedModules?: string[];
  /** 是否允许执行系统命令 */
  allowSystemCommands?: boolean;
}
