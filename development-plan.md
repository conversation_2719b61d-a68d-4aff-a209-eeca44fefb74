# 开发计划

## 项目目标
开发一款基于 Electron 的终端软件，支持 SSH、SFTP、SerialPort 等功能，并以插件形式扩展功能。

## 实现步骤

### 1. 初始化项目
- 使用 `pnpm` 和 `electron-vite` 初始化项目。
- 配置 TypeScript 和 Vue。

### 2. 实现主程序
- 搭建 Electron 主进程和渲染进程的通信机制。
- 提供插件加载和管理的基础能力。

### 3. 开发插件系统
- 定义插件的元数据格式（`manifest.json`）。
- 实现插件的动态加载和卸载功能。
- 创建示例插件，包含激活和停用逻辑。

### 4. 设计用户界面
- 使用 Vue 构建主界面。
- 添加插件管理页面，展示已加载插件列表。
- 提供插件安装和卸载功能。

### 5. 修复问题
- 修复 Electron 安装失败的问题：
  - 删除 `node_modules` 和 Electron 缓存，重新安装依赖。
- 修复渲染进程中动态导入 `electron` 的问题：
  - 使用 `preload.mjs` 和 `contextBridge` 暴露 `ipcRenderer`。
  - 修改渲染进程代码，通过 `window.ipcRenderer` 访问 Electron 功能。

## 当前状态
- 项目初始化完成。
- 主程序支持插件加载。
- 插件管理页面已实现，支持插件的安装和卸载。
- 修复了 Electron 安装和动态导入问题。

## 下一步计划
- 优化插件系统，支持更多功能扩展。
- 增加插件市场，支持在线浏览和安装插件。
- 提高界面美观性和用户体验。

---
记录日期：2025年6月18日
