{"name": "terminal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build && electron-builder", "preview": "vite preview"}, "dependencies": {"vue": "^3.4.21"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "typescript": "^5.2.2", "vite": "^5.1.6", "vue-tsc": "^2.0.26", "electron": "^30.0.1", "electron-builder": "^24.13.3", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5"}, "main": "dist-electron/main.js", "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "cpu-features", "electron", "esbuild", "node-pty", "simple-git-hooks", "ssh2"]}}