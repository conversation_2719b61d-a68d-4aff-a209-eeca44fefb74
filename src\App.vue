<script setup lang="ts">
import { ref } from 'vue';
import PluginManager from './views/PluginManager.vue';

const executing = ref(false);
const executionMessage = ref('');
const executionError = ref('');

async function execFeature(pluginName: string, featureName: string = 'activate') {
  try {
    executing.value = true;
    executionMessage.value = `执行插件 ${pluginName} 的 ${featureName} 功能...`;
    executionError.value = '';

    const feature = await window.ipcRenderer.invoke('get-plugin-feature', pluginName, featureName);

    if (typeof feature === 'function') {
      const result = await feature();
      executionMessage.value = `插件 ${pluginName} 执行成功`;

      // 如果有返回结果，显示给用户
      if (result !== undefined) {
        console.log('Plugin execution result:', result);
      }
    } else {
      throw new Error('获取的功能不是一个函数');
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '执行插件功能时发生未知错误';
    executionError.value = `执行失败: ${errorMessage}`;
    console.error('Plugin execution failed:', error);
  } finally {
    executing.value = false;
    // 3秒后清除消息
    setTimeout(() => {
      executionMessage.value = '';
      executionError.value = '';
    }, 3000);
  }
}

function clearMessages() {
  executionMessage.value = '';
  executionError.value = '';
}
</script>

<template>
  <div class="app">
    <header class="app-header">
      <h1>插件系统演示</h1>
    </header>

    <main class="app-main">
      <!-- 执行状态显示 -->
      <div v-if="executionMessage" class="execution-message success">
        {{ executionMessage }}
      </div>

      <div v-if="executionError" class="execution-message error">
        {{ executionError }}
        <button @click="clearMessages" class="close-btn">×</button>
      </div>

      <!-- 快速执行区域 -->
      <section class="quick-actions">
        <h2>快速执行</h2>
        <div class="action-buttons">
          <button
            @click="execFeature('Example', 'activate')"
            :disabled="executing"
            class="action-btn"
          >
            {{ executing ? '执行中...' : '执行示例插件' }}
          </button>
        </div>
      </section>

      <!-- 插件管理器 -->
      <PluginManager />
    </main>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 0;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.app-header h1 {
  margin: 0;
  font-size: 2.5em;
  font-weight: 300;
}

.app-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 执行状态消息 */
.execution-message {
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.execution-message.success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.execution-message.error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
  opacity: 0.7;
}

.close-btn:hover {
  opacity: 1;
}

/* 快速执行区域 */
.quick-actions {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 32px;
}

.quick-actions h2 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  font-size: 1.5em;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.action-btn:active:not(:disabled) {
  transform: translateY(0);
}
</style>
