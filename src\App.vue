<script setup lang="ts">
import PluginManager from './views/PluginManager.vue';
function execFeature(feature: string) {
  window.ipcRenderer.invoke('get-plugin-feature', feature, 'feature').then(feature => {
    feature(); // 调用插件提供的功能
  });
}
</script>

<template>
  <button @click="execFeature('Example')">执行</button>
  <PluginManager />
</template>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
