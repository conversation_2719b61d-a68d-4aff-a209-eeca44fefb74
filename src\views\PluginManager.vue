<template>
  <div class="plugin-manager">
    <h1>插件管理</h1>

    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <strong>错误:</strong> {{ error }}
      <button @click="clearError" class="close-btn">×</button>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="success-message">
      <strong>成功:</strong> {{ successMessage }}
      <button @click="clearSuccess" class="close-btn">×</button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-message">
      <span class="spinner"></span> {{ loadingMessage }}
    </div>

    <!-- 插件安装 -->
    <div class="install-section">
      <h2>安装插件</h2>
      <input
        type="file"
        @change="installPlugin"
        :disabled="loading"
        accept=".js,.zip"
      />
    </div>

    <!-- 插件列表 -->
    <div class="plugins-section">
      <h2>已安装插件 ({{ plugins.length }})</h2>
      <div v-if="plugins.length === 0" class="no-plugins">
        暂无已安装的插件
      </div>
      <div v-else class="plugins-grid">
        <div v-for="plugin in plugins" :key="plugin.name" class="plugin-card">
          <div class="plugin-header">
            <h3>{{ plugin.name }}</h3>
            <span :class="['status-badge', plugin.status]">
              {{ getStatusText(plugin.status) }}
            </span>
          </div>
          <p class="plugin-description">{{ plugin.description }}</p>
          <div v-if="plugin.version" class="plugin-version">
            版本: {{ plugin.version }}
          </div>
          <div v-if="plugin.error" class="plugin-error">
            错误: {{ plugin.error }}
          </div>
          <div class="plugin-actions">
            <button
              @click="uninstallPlugin(plugin.name)"
              :disabled="loading"
              class="uninstall-btn"
            >
              卸载
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import type { PluginInfo } from '../types/plugin';

const ipcRenderer = (window as any).ipcRenderer;

export default defineComponent({
  name: 'PluginManager',
  setup() {
    const plugins = ref<PluginInfo[]>([]);
    const loading = ref(false);
    const loadingMessage = ref('');
    const error = ref('');
    const successMessage = ref('');

    onMounted(() => {
      loadPlugins();
    });

    const loadPlugins = async () => {
      try {
        loading.value = true;
        loadingMessage.value = '加载插件列表...';
        clearMessages();

        const loadedPlugins: PluginInfo[] = await ipcRenderer.invoke('get-plugins');
        plugins.value = loadedPlugins;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '加载插件列表失败';
        error.value = errorMessage;
        console.error('Failed to load plugins:', err);
      } finally {
        loading.value = false;
        loadingMessage.value = '';
      }
    };

    const installPlugin = async (event: Event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        loading.value = true;
        loadingMessage.value = '安装插件中...';
        clearMessages();

        const result = await ipcRenderer.invoke('install-plugin', file.path);

        if (result.success) {
          successMessage.value = result.message;
          await loadPlugins();
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '插件安装失败';
        error.value = errorMessage;
        console.error('Plugin installation failed:', err);
      } finally {
        loading.value = false;
        loadingMessage.value = '';
        // 清空文件输入
        (event.target as HTMLInputElement).value = '';
      }
    };

    const uninstallPlugin = async (pluginName: string) => {
      if (!confirm(`确定要卸载插件 "${pluginName}" 吗？`)) {
        return;
      }

      try {
        loading.value = true;
        loadingMessage.value = '卸载插件中...';
        clearMessages();

        const result = await ipcRenderer.invoke('uninstall-plugin', pluginName);

        if (result.success) {
          successMessage.value = result.message;
          await loadPlugins();
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '插件卸载失败';
        error.value = errorMessage;
        console.error('Plugin uninstallation failed:', err);
      } finally {
        loading.value = false;
        loadingMessage.value = '';
      }
    };

    const clearMessages = () => {
      error.value = '';
      successMessage.value = '';
    };

    const clearError = () => {
      error.value = '';
    };

    const clearSuccess = () => {
      successMessage.value = '';
    };

    const getStatusText = (status: string) => {
      const statusMap: Record<string, string> = {
        loading: '加载中',
        loaded: '已加载',
        failed: '加载失败',
        unknown: '未知状态'
      };
      return statusMap[status] || status;
    };

    return {
      plugins,
      loading,
      loadingMessage,
      error,
      successMessage,
      installPlugin,
      uninstallPlugin,
      clearError,
      clearSuccess,
      getStatusText,
    };
  },
});
</script>

<style scoped>
.plugin-manager {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 消息提示样式 */
.error-message, .success-message {
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message {
  background-color: #fee;
  border: 1px solid #fcc;
  color: #c33;
}

.success-message {
  background-color: #efe;
  border: 1px solid #cfc;
  color: #363;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
  opacity: 0.7;
}

.close-btn:hover {
  opacity: 1;
}

/* 加载状态样式 */
.loading-message {
  padding: 12px 16px;
  margin-bottom: 16px;
  background-color: #f0f8ff;
  border: 1px solid #b3d9ff;
  border-radius: 6px;
  color: #0066cc;
  display: flex;
  align-items: center;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #b3d9ff;
  border-top: 2px solid #0066cc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 安装区域样式 */
.install-section {
  margin-bottom: 32px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.install-section h2 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
}

.install-section input[type="file"] {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
}

.install-section input[type="file"]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 插件列表样式 */
.plugins-section h2 {
  margin-bottom: 16px;
  color: #333;
}

.no-plugins {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

.plugins-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.plugin-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: box-shadow 0.2s;
}

.plugin-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.plugin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.plugin-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-badge.loaded {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.loading {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status-badge.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.status-badge.unknown {
  background-color: #e2e3e5;
  color: #383d41;
}

.plugin-description {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.plugin-version {
  font-size: 12px;
  color: #888;
  margin-bottom: 8px;
}

.plugin-error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-bottom: 12px;
  border: 1px solid #f5c6cb;
}

.plugin-actions {
  display: flex;
  gap: 8px;
}

.uninstall-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.uninstall-btn:hover:not(:disabled) {
  background-color: #c82333;
}

.uninstall-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
