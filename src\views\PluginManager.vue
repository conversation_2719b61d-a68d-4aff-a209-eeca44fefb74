<template>
  <!-- 添加上传插件的功能 -->
  <div class="plugin-manager">
    <h1>插件管理</h1>
    <input type="file" @change="installPlugin" />
    <ul>
      <li v-for="plugin in plugins" :key="plugin.name">
        <strong>{{ plugin.name }}</strong> - {{ plugin.description }}
        <button @click="uninstallPlugin(plugin.name)">卸载</button>
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';

// 为插件定义类型
interface Plugin {
  name: string;
  description: string;
}

export default defineComponent({
  name: 'PluginManager',
  setup() {
    // 修改 plugins 的类型
    const plugins = ref<Plugin[]>([]);

    onMounted(() => {
      loadPlugins();
    });

    const loadPlugins = () => {
      // 为 loadedPlugins 显式指定类型
      ipcRenderer.invoke('get-plugins').then((loadedPlugins: Plugin[]) => {
        plugins.value = loadedPlugins;
      });
    };

    const installPlugin = (event: Event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file) {
        ipcRenderer.invoke('install-plugin', file.path).then(() => {
          loadPlugins();
        });
      }
    };

    const uninstallPlugin = (pluginName: string) => {
      ipcRenderer.invoke('uninstall-plugin', pluginName).then(() => {
        loadPlugins();
      });
    };

    return {
      plugins,
      installPlugin,
      uninstallPlugin,
    };
  },
});

const ipcRenderer = (window as any).ipcRenderer;
</script>

<style scoped>
.plugin-manager {
  padding: 20px;
}
</style>
